﻿namespace SimpleBooks.Services.API.Business.Purchases
{
    public class PurchaseOrderService : SimpleBooksBaseService<PurchaseOrderModel, IndexPurchaseOrderViewModel, CreatePurchaseOrderViewModel, UpdatePurchaseOrderViewModel>, IPurchaseOrderService
    {
        private readonly IVendorService _vendorService;
        private readonly IVendorTypeService _vendorTypeService;
        private readonly IPaymentTermService _paymentTermService;
        private readonly IProductService _productService;
        private readonly IUnitService _unitService;

        public PurchaseOrderService(
            IAuthenticationValidationService authenticationValidationService,
            IHttpClientFactory httpClientFactory,
            IVendorService vendorService,
            IVendorTypeService vendorTypeService,
            IPaymentTermService paymentTermService,
            IProductService productService,
            IUnitService unitService) : base(authenticationValidationService, httpClientFactory)
        {
            _vendorService = vendorService;
            _vendorTypeService = vendorTypeService;
            _paymentTermService = paymentTermService;
            _productService = productService;
            _unitService = unitService;
        }

        public async Task<ServiceResult<IEnumerable<VendorDto>>> SelectiveVendorListAsync() => await _vendorService.SelectiveVendorDtoListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveVendorTypeListAsync() => await _vendorTypeService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<PaymentTermDto>>> SelectivePaymentTermListAsync() => await _paymentTermService.SelectivePaymentTermDtoListAsync();
        public async Task<ServiceResult<IEnumerable<ProductModel>>> SelectiveProductListAsync() => await _productService.GetAllAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveUnitListAsync() => await _unitService.GetSelectListAsync();
    }
}
