﻿namespace SimpleBooks.Models.Model.Purchases
{
    [Table("Vendor")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<VendorModel>))]
    public class VendorModel : BaseWithTrackingModel
    {
        [CustomRequired]
        [DisplayName("Vendor Name")]
        public string VendorName { get; set; }
        [CustomRequired]
        [StringLength(9)]
        [DisplayName("Vendor Tax Card Number")]
        public string VendorTaxCardNumber { get; set; }

        [DisplayName("Vendor Type")]
        public Ulid? VendorTypeId { get; set; }
        public virtual VendorTypeModel? VendorType { get; set; }

        [DisplayName("Payment Term")]
        public Ulid? PaymentTermId { get; set; }
        public virtual PaymentTermModel? PaymentTerm { get; set; }

        public virtual ICollection<BillModel> Bills { get; set; } = new List<BillModel>();
        public virtual ICollection<BillReturnModel> BillReturns { get; set; } = new List<BillReturnModel>();
        public virtual ICollection<PurchaseOrderModel> PurchaseOrders { get; set; } = new List<PurchaseOrderModel>();
        [DisplayName("Cash Treasury Vouchers")]
        public virtual ICollection<CashTreasuryVoucherModel> CashTreasuryVouchers { get; set; } = new List<CashTreasuryVoucherModel>();
        [DisplayName("Check Treasury Vouchers")]
        public virtual ICollection<CheckTreasuryVoucherModel> CheckTreasuryVouchers { get; set; } = new List<CheckTreasuryVoucherModel>();
        [DisplayName("Bank Transfer Treasury Vouchers")]
        public virtual ICollection<BankTransferTreasuryVoucherModel> BankTransferTreasuryVouchers { get; set; } = new List<BankTransferTreasuryVoucherModel>();
    }
}
