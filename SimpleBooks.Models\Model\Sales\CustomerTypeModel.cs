﻿namespace SimpleBooks.Models.Model.Sales
{
    [Table("CustomerType")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<CustomerTypeModel>))]
    public class CustomerTypeModel : BaseWithoutTrackingModel
    {
        [CustomRequired]
        [DisplayName("Customer Type Name")]
        public string CustomerTypeName { get; set; }

        public virtual ICollection<CustomerModel> Customers { get; set; } = new List<CustomerModel>();
        public virtual ICollection<InvoiceModel> Invoices { get; set; } = new List<InvoiceModel>();
        public virtual ICollection<InvoiceReturnModel> InvoiceReturns { get; set; } = new List<InvoiceReturnModel>();
        public virtual ICollection<SalesOrderModel> SalesOrders { get; set; } = new List<SalesOrderModel>();
    }
}
