﻿namespace SimpleBooks.Models.ViewModel.Sales.SalesOrder
{
    public class UpdateSalesOrderViewModel : BaseUpdateViewModel, IEntityMapper<SalesOrderModel, UpdateSalesOrderViewModel>
    {
        [CustomRequired]
        [DisplayName("Sales Order Id")]
        public string SalesOrderId { get; set; }
        [CustomRequired]
        [DisplayName("Sales Order Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateOnly SalesOrderDate { get; set; }
        [CustomRequired]
        [DisplayName("Sales Order Due Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateOnly SalesOrderDueDate { get; set; }

        [CustomRequired]
        [DisplayName("Customer")]
        public Ulid CustomerId { get; set; }
        [DisplayName("Customer Type")]
        public Ulid? CustomerTypeId { get; set; }
        [DisplayName("Customer Sales Rep")]
        public Ulid? CustomerRepId { get; set; }
        [DisplayName("Payment Term")]
        public Ulid? PaymentTermId { get; set; }

        [CustomRequired]
        [DisplayName("Sales Order Lines")]
        public IList<UpdateSalesOrderLineViewModel> SalesOrderLines { get; set; } = new List<UpdateSalesOrderLineViewModel>();

        public UpdateSalesOrderViewModel ToDto(SalesOrderModel entity) => entity.ToUpdateDto();

        public SalesOrderModel ToEntity() => SalesOrderMapper.ToEntity(this);
    }
}
