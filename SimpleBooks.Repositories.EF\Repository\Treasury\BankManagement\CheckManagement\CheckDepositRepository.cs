﻿namespace SimpleBooks.Repositories.EF.Repository.Treasury.BankManagement.CheckManagement
{
    internal class CheckDepositRepository : SimpleBooksBaseRepository<CheckDepositModel, CheckDepositModel>
    {
        public CheckDepositRepository(ApplicationDBContext context) : base(context)
        {
        }

        protected override Expression<Func<CheckDepositModel, object>> OrderByColumn => x => x.Id;

        public override async Task<CheckDepositModel?> AddAsync(CheckDepositModel? model)
        {
            if (model == null)
                throw new Exception("Model cannot be null");

            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var newIds = model.CheckTreasuryVouchers.Select(x => x.Id).ToHashSet();

                var specs = new RepositorySpecifications<CheckTreasuryVoucherModel>
                {
                    Includes = x => x.Include(xx => xx.CheckStatusHistories),
                    SearchValue = x => newIds.Contains(x.Id) && x.CheckStatusId == CheckStatusEnumeration.Received.Value,
                    IsTackable = true,
                };

                var selectedChecks = await GetQueryable(specs).ToListAsync();

                var missingIds = newIds.Except(selectedChecks.Select(x => x.Id)).ToList();
                if (missingIds.Any())
                    throw new Exception($"Invalid check(s): {string.Join(", ", missingIds)}");

                foreach (var check in selectedChecks)
                {
                    check.CheckStatusId = CheckStatusEnumeration.Deposited.Value;
                    check.CheckDepositId = model.Id;

                    model.CheckStatusHistories.Add(new CheckStatusHistoryModel
                    {
                        TransactionDate = model.DepositDate,
                        Note = $"Check deposited on {model.DepositDate:dd/MM/yyyy}",
                        CheckStatusFromId = CheckStatusEnumeration.Received.Value,
                        CheckStatusToId = CheckStatusEnumeration.Deposited.Value,
                        CheckTreasuryVoucherId = check.Id,
                        CheckDepositId = model.Id
                    });
                }

                model.CheckTreasuryVouchers = selectedChecks;

                var result = await _context.Set<CheckDepositModel>().AddAsync(model);
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return result.Entity;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public override async Task<CheckDepositModel?> UpdateAsync(CheckDepositModel? model)
        {
            if (model == null)
                throw new Exception("Model cannot be null");
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var spec = new RepositorySpecifications<CheckDepositModel>
                {
                    Includes = x => x
                        .Include(d => d.CheckTreasuryVouchers)
                        .ThenInclude(c => c.CheckStatusHistories),
                    SearchValue = x => x.Id == model.Id,
                    IsTackable = true
                };

                var existingDepositQuery = GetQueryable(spec);

                var existingDeposit = await existingDepositQuery.FirstOrDefaultAsync();
                if (existingDeposit == null)
                    throw new Exception("Deposit not found");
                var existingChecks = existingDeposit.CheckTreasuryVouchers.ToList();
                var newCheckIds = model.CheckTreasuryVouchers.Select(c => c.Id).ToHashSet();
                var oldCheckIds = existingChecks.Select(c => c.Id).ToHashSet();

                // 1. Checks to be removed
                var removedChecks = existingChecks.Where(c => !newCheckIds.Contains(c.Id)).ToList();

                foreach (var check in removedChecks)
                {
                    check.CheckStatusId = CheckStatusEnumeration.Received.Value;
                    check.CheckDepositId = null;

                    var depositHistory = check.CheckStatusHistories
                        .FirstOrDefault(h => h.CheckDepositId == existingDeposit.Id && h.CheckStatusToId == CheckStatusEnumeration.Deposited.Value);

                    if (depositHistory != null)
                        _context.Set<CheckStatusHistoryModel>().Remove(depositHistory);
                }

                // 2. Checks to be added
                var addedCheckIds = newCheckIds.Except(oldCheckIds).ToList();
                var newCheckSpecs = new RepositorySpecifications<CheckTreasuryVoucherModel>
                {
                    Includes = x => x.Include(xx => xx.CheckStatusHistories),
                    SearchValue = x => addedCheckIds.Contains(x.Id) && x.CheckStatusId == CheckStatusEnumeration.Received.Value,
                    IsTackable = true
                };
                var addedChecks = GetQueryable(newCheckSpecs);

                foreach (var check in addedChecks)
                {
                    check.CheckStatusId = CheckStatusEnumeration.Deposited.Value;
                    check.CheckDepositId = existingDeposit.Id;

                    check.CheckStatusHistories.Add(new CheckStatusHistoryModel
                    {
                        TransactionDate = model.DepositDate,
                        Note = $"Check deposited on {model.DepositDate:dd/MM/yyyy}",
                        CheckStatusFromId = CheckStatusEnumeration.Received.Value,
                        CheckStatusToId = CheckStatusEnumeration.Deposited.Value,
                        CheckTreasuryVoucherId = check.Id,
                        CheckDepositId = existingDeposit.Id
                    });
                }

                // 3. Remaining checks that were not changed
                var retainedChecks = existingChecks.Where(c => newCheckIds.Contains(c.Id)).ToList();

                // 4. Final update: fully replace the list with only the updated list
                existingDeposit.CheckTreasuryVouchers = retainedChecks.Concat(addedChecks).ToList();
                existingDeposit.DepositDate = model.DepositDate;
                existingDeposit.RefranceNumber = model.RefranceNumber;
                existingDeposit.BankId = model.BankId;
                existingDeposit.BankAccountId = model.BankAccountId;

                var result = _context.Set<CheckDepositModel>().Update(existingDeposit);
                await _context.SaveChangesAsync();
                return result.Entity;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }
    }
}
