﻿namespace SimpleBooks.Models.Model.Sales
{
    [Table("SalesOrderLine")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<SalesOrderLineModel>))]
    public class SalesOrderLineModel : BaseWithoutTrackingModel
    {
        [CustomRequired]
        [DisplayName("Quantity")]
        public decimal Quantity { get; set; }
        [CustomRequired]
        [DisplayName("Price")]
        public decimal Price { get; set; }
        [CustomRequired]
        [DisplayName("Amount")]
        public decimal Amount { get; set; }
        [CustomRequired]
        [DisplayName("Unit Qty Ratio")]
        public decimal UnitQtyRatio { get; set; }

        [CustomRequired]
        [DisplayName("Product")]
        public Ulid ProductId { get; set; }
        public virtual ProductModel? Product { get; set; }

        [CustomRequired]
        [DisplayName("Product Unit")]
        public Ulid ProductUnitId { get; set; }
        public virtual UnitModel? ProductUnit { get; set; }

        [DisplayName("SalesOrder")]
        public Ulid SalesOrderId { get; set; }
        public virtual SalesOrderModel? SalesOrder { get; set; }

        [DisplayName("Inventories")]
        public virtual ICollection<InventoryModel> Inventories { get; set; } = new List<InventoryModel>();
    }
}
