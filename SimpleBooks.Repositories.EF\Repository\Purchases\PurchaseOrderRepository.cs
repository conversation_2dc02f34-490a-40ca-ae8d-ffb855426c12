﻿namespace SimpleBooks.Repositories.EF.Repository.Purchases
{
    internal class PurchaseOrderRepository : SimpleBooksBaseRepository<PurchaseOrderModel, IndexPurchaseOrderViewModel>, IPurchaseOrderRepository<PurchaseOrderModel, IndexPurchaseOrderViewModel>
    {
        public PurchaseOrderRepository(ApplicationDBContext context) : base(context)
        {
        }

        protected override Expression<Func<IndexPurchaseOrderViewModel, string>>[] NamesOfSearchView => [x => x.PurchaseOrderId, x => x.VendorName];
        protected override IQueryable<IndexPurchaseOrderViewModel> ViewQueryable => PurchaseOrderQueries.PurchaseOrderViewQuery(_context);
        protected override string ViewQuery => PurchaseOrderQueries.PurchaseOrderViewQuery(_context).ToQueryString();
        protected override Expression<Func<PurchaseOrderModel, string>> NameSelector => x => x.PurchaseOrderId;
        protected override Expression<Func<IndexPurchaseOrderViewModel, object>> OrderByColumn => x => x.Id;

        public async Task<IEnumerable<OpenPurchaseOrderDto>> GetOpenPurchaseOrdersByVendorAsync(Ulid vendorId)
        {
            if (vendorId == Ulid.Empty)
                throw new ArgumentException("Vendor ID cannot be empty.", nameof(vendorId));

            RepositorySpecifications<PurchaseOrderModel> purchaseOrdersRepositorySpecifications = new RepositorySpecifications<PurchaseOrderModel>()
            {
                SearchValue = x => x.VendorId == vendorId,
                IsTackable = false,
                OrderBy = x => x.PurchaseOrderId,
                OrderByType = OrderByType.Descending,
            };

            var purchaseOrdersQuery = GetQueryable(purchaseOrdersRepositorySpecifications);

            var result = await purchaseOrdersQuery.Where(
                order => order.PurchaseOrderLines.Any(
                    line => line.Quantity > line.Inventories.Where(
                        inv => inv.ProductId == line.ProductId).Sum(
                        inv => inv.Quantity)
                        )
                )
                .Select(order => new OpenPurchaseOrderDto()
                {
                    Id = order.Id,
                    PurchaseOrderId = order.PurchaseOrderId,
                    PurchaseOrderDate = order.PurchaseOrderDate,
                    PurchaseOrderDueDate = order.PurchaseOrderDueDate,
                    VendorId = order.VendorId,
                    VendorTypeId = order.VendorTypeId,
                    PaymentTermId = order.PaymentTermId,
                }).ToListAsync();

            return result;
        }

        public async Task<IEnumerable<OpenPurchaseOrderLineDto>> GetOpenPurchaseOrderLinesAsync(Ulid purchaseOrderId)
        {
            if (purchaseOrderId == Ulid.Empty)
                throw new ArgumentException("Purchase Order ID cannot be empty.", nameof(purchaseOrderId));

            RepositorySpecifications<PurchaseOrderLineModel> purchaseOrderLinesRepositorySpecifications = new RepositorySpecifications<PurchaseOrderLineModel>()
            {
                SearchValue = x => x.PurchaseOrderId == purchaseOrderId,
                Includes = x => x.Include(xx => xx.Inventories),
                IsTackable = false,
                OrderBy = x => x.PurchaseOrderId,
                OrderByType = OrderByType.Descending,
            };

            var purchaseOrderLinesQuery = GetQueryable(purchaseOrderLinesRepositorySpecifications);

            var result = await purchaseOrderLinesQuery.Where(
                    line => line.Quantity > line.Inventories.Where(
                        inv => inv.ProductId == line.ProductId).Sum(
                        inv => inv.Quantity)
                        )
                .Select(line => new OpenPurchaseOrderLineDto()
                {
                    Id = line.Id,
                    Quantity = line.Quantity - line.Inventories.Where(inv => inv.ProductId == line.ProductId).Sum(inv => inv.Quantity),
                    Price = line.Price,
                    Amount = line.Amount,
                    UnitQtyRatio = line.UnitQtyRatio,
                    ProductId = line.ProductId,
                    ProductUnitId = line.ProductUnitId,
                    PurchaseOrderId = line.PurchaseOrderId
                }).ToListAsync();

            return result;
        }
    }
}
