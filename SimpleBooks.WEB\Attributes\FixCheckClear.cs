﻿namespace SimpleBooks.WEB.Attributes
{
    public class FixCheckClear : ActionFilterAttribute
    {
        public override void OnActionExecuting(ActionExecutingContext context)
        {
            if (context.ActionArguments.ContainsKey("model"))
            {
                var model = context.ActionArguments["model"];

                if (model is null)
                    return;

                switch (model)
                {
                    case CreateCheckClearFormViewModel baseCreateViewModel:
                        SetModel(context, baseCreateViewModel);
                        break;
                    case UpdateCheckClearFormViewModel baseUpdateViewModel:
                        SetModel(context, baseUpdateViewModel);
                        break;
                }
            }

            base.OnActionExecuting(context);
        }

        private void SetModel(ActionExecutingContext context, CreateCheckClearFormViewModel model)
        {
            if (model != null)
            {
                var checkTreasuryVouchers = JsonConvert.DeserializeObject<List<CheckTreasuryVoucherModel>>(model.SelectedChecksJson) ?? new List<CheckTreasuryVoucherModel>();
                model.SelectedCheckTreasuryVouchers = checkTreasuryVouchers.Select(x => x.Id).ToList();
                UpdateModelState(context, "SelectedCheckTreasuryVouchers", model.SelectedCheckTreasuryVouchers);
            }
        }

        private void SetModel(ActionExecutingContext context, UpdateCheckClearFormViewModel model)
        {
            if (model != null)
            {
                var checkTreasuryVouchers = JsonConvert.DeserializeObject<List<CheckTreasuryVoucherModel>>(model.SelectedChecksJson) ?? new List<CheckTreasuryVoucherModel>();
                model.SelectedCheckTreasuryVouchers = checkTreasuryVouchers.Select(x => x.Id).ToList();
                UpdateModelState(context, "SelectedCheckTreasuryVouchers", model.SelectedCheckTreasuryVouchers);
            }
        }

        private void UpdateModelState(ActionExecutingContext context, string key, object value)
        {
            if (context.Controller is Controller controller)
            {
                controller.ModelState.SetModelValue(key, value, value?.ToString());
                controller.ModelState.ClearValidationState(key);
                var model = context.ActionArguments["model"];
                if (model is null)
                    return;
                controller.TryValidateModel(model);
            }
        }
    }
}
