﻿namespace SimpleBooks.Repositories.EF.Factory.ConstLinqQueries
{
    internal class SalesOrderQueries
    {
        public static IQueryable<IndexSalesOrderViewModel> SalesOrderViewQuery(ApplicationDBContext dbContext) => (from invoice in dbContext.SalesOrders
                                                                                                             .Include(x => x.Customer)
                                                                                                             .Include(x => x.CustomerType)
                                                                                                             .Include(x => x.CustomerRep)
                                                                                                             .Include(x => x.PaymentTerm)
                                                                                                             .Include(x => x.SalesOrderLines).AsNoTracking()
                                                                                                                   select new IndexSalesOrderViewModel
                                                                                                                   {
                                                                                                                       Id = invoice.Id,
                                                                                                                       SalesOrderId = invoice.SalesOrderId,
                                                                                                                       SalesOrderDate = invoice.SalesOrderDate,
                                                                                                                       SalesOrderDueDate = invoice.SalesOrderDueDate,
                                                                                                                       CustomerName = invoice.Customer != null ? invoice.Customer.CustomerName : string.Empty,
                                                                                                                       CustomerTypeName = invoice.CustomerType != null ? invoice.CustomerType.CustomerTypeName : string.Empty,
                                                                                                                       CustomerRepName = invoice.CustomerRep != null ? invoice.CustomerRep.EmployeeName : string.Empty,
                                                                                                                       PaymentTermName = invoice.PaymentTerm != null ? invoice.PaymentTerm.PaymentTermName : string.Empty,
                                                                                                                       Amount = invoice.SalesOrderLines.Sum(x => x.Amount),
                                                                                                                   });
    }
}
