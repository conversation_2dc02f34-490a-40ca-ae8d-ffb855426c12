﻿namespace SimpleBooks.Models.Enumerations
{
    public class TransactionTypeEnumeration : UlidEnumeration<TransactionTypeEnumeration>
    {
        public static readonly TransactionTypeEnumeration Bill = new TransactionTypeEnumeration(Ulid.Parse("01JRKA5XSNHEQWKTR0NCJWYC6W"), "Bill");
        public static readonly TransactionTypeEnumeration BillReturn = new TransactionTypeEnumeration(Ulid.Parse("01JRKA5XSQ45QDM1C7H1TME01A"), "Bill Return");
        public static readonly TransactionTypeEnumeration Invoice = new TransactionTypeEnumeration(Ulid.Parse("01JRKA5XSQSFE4H3R3M177EDN5"), "Invoice");
        public static readonly TransactionTypeEnumeration InvoiceReturn = new TransactionTypeEnumeration(Ulid.Parse("01JRKA5XSQGDQ36JWP7BFAE06R"), "Invoice Return");
        public static readonly TransactionTypeEnumeration InventoryAdjustment = new TransactionTypeEnumeration(Ulid.Parse("01JRKA5XSQ95F5J1D2DBHHM375"), "Inventory Adjustment");
        public static readonly TransactionTypeEnumeration InventoryTransfer = new TransactionTypeEnumeration(Ulid.Parse("01JRKA5XSR7RW0JNTPY9X6JPBV"), "Inventory Transfer");
        public static readonly TransactionTypeEnumeration InventoryOpeningBalance = new TransactionTypeEnumeration(Ulid.Parse("01JRKA5XSRKZCHG3CMFM9ZPRN7"), "Inventory Opening Balance");
        public static readonly TransactionTypeEnumeration SalesOrder = new TransactionTypeEnumeration(Ulid.Parse("01JRKA5XSRQHRCBY9A06M2J1RQ"), "SalesOrder");
        public static readonly TransactionTypeEnumeration PurchaseOrder = new TransactionTypeEnumeration(Ulid.Parse("01K0SHCQVBWFD8NYJ5213AKVH3"), "SalesOrder");
        public static readonly TransactionTypeEnumeration TreasuryCashIn = new(Ulid.Parse("01JZBSNG4F3P7EYRA6T1JY5HPC"), "Cash In");
        public static readonly TransactionTypeEnumeration TreasuryCashOut = new(Ulid.Parse("01JZBSNQWAHRA4CK6TKK9SVY43"), "Cash Out");
        public static readonly TransactionTypeEnumeration TreasuryCheckIn = new(Ulid.Parse("01JZBSP3PFVH37SZ8D5DEQF304"), "Check In");
        public static readonly TransactionTypeEnumeration TreasuryCheckOut = new(Ulid.Parse("01JZBSP7B64B1GAKF49A3D8M9S"), "Check Out");
        public static readonly TransactionTypeEnumeration TreasuryBankTransferIn = new(Ulid.Parse("01JZBSPCXBX6NRP69E2X96YSW5"), "Bank Transfer In");
        public static readonly TransactionTypeEnumeration TreasuryBankTransferOut = new(Ulid.Parse("01JZBSPK8EG8Z9AVT9K2KQ3XXC"), "Bank Transfer Out");
        public static readonly TransactionTypeEnumeration TreasuryBankIn = new(Ulid.Parse("01JZBSNVFRKWDYN58VC8Y5CPXM"), "Bank In");
        public static readonly TransactionTypeEnumeration TreasuryBankOut = new(Ulid.Parse("01JZBSP0S7B0C5T3YXWJ2WSGPP"), "Bank Out");

        private TransactionTypeEnumeration(Ulid key, string value) : base(key, value)
        {
        }

        public static List<TransactionTypeEnumeration> TransactionTypeEnumerations
        {
            get => new List<TransactionTypeEnumeration>
            {
                Bill, BillReturn, Invoice, InvoiceReturn,
                InventoryAdjustment, InventoryTransfer, InventoryOpeningBalance,
                SalesOrder,
                TreasuryCashIn, TreasuryCashOut, TreasuryCheckIn, TreasuryCheckOut, TreasuryBankTransferIn, TreasuryBankTransferOut, TreasuryBankIn, TreasuryBankOut,
            };
        }

        public static List<TransactionTypeEnumeration> TreasuryTransactionTypeEnumerations
        {
            get => new List<TransactionTypeEnumeration>()
                .Concat(TreasuryCashTransactionTypeEnumerations)
                .Concat(TreasuryCheckTransactionTypeEnumerations)
                .Concat(TreasuryBankTransactionTypeEnumerations)
                .ToList();
        }

        public static List<TransactionTypeEnumeration> TreasuryCashTransactionTypeEnumerations
        {
            get => new List<TransactionTypeEnumeration>
            {
                TreasuryCashIn, TreasuryCashOut,
            };
        }

        public static List<TransactionTypeEnumeration> TreasuryCheckTransactionTypeEnumerations
        {
            get => new List<TransactionTypeEnumeration>
            {
                TreasuryCheckIn, TreasuryCheckOut,
            };
        }

        public static List<TransactionTypeEnumeration> TreasuryBankTransactionTypeEnumerations
        {
            get => new List<TransactionTypeEnumeration>
            {
                TreasuryBankTransferIn, TreasuryBankTransferOut, TreasuryBankIn, TreasuryBankOut,
            };
        }

        public static bool IsTreasuryRelated(Ulid id) => TreasuryTransactionTypeEnumerations.Any(x => x.Value == id);
        public static bool IsTreasuryCashRelated(Ulid id) => TreasuryCashTransactionTypeEnumerations.Any(x => x.Value == id);
        public static bool IsTreasuryCheckRelated(Ulid id) => TreasuryCheckTransactionTypeEnumerations.Any(x => x.Value == id);
        public static bool IsTreasuryBankRelated(Ulid id) => TreasuryBankTransactionTypeEnumerations.Any(x => x.Value == id);
    }
}
