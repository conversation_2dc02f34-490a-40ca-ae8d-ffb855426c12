﻿namespace SimpleBooks.Repositories.EF.Repository.Treasury.BankManagement.CheckManagement
{
    internal class CheckCollectionRepository : SimpleBooksBaseRepository<CheckCollectionModel, CheckCollectionModel>
    {
        public CheckCollectionRepository(ApplicationDBContext context) : base(context)
        {
        }

        protected override Expression<Func<CheckCollectionModel, object>> OrderByColumn => x => x.Id;

        public override async Task<CheckCollectionModel?> AddAsync(CheckCollectionModel? model)
        {
            if (model == null)
                throw new Exception("Model cannot be null");

            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var newIds = model.CheckTreasuryVouchers.Select(x => x.Id).ToHashSet();

                var specs = new RepositorySpecifications<CheckTreasuryVoucherModel>
                {
                    Includes = x => x.Include(xx => xx.CheckStatusHistories),
                    SearchValue = x => newIds.Contains(x.Id) && x.CheckStatusId == CheckStatusEnumeration.Deposited.Value,
                    IsTackable = true,
                };

                var selectedChecks = await GetQueryable(specs).ToListAsync();

                var missingIds = newIds.Except(selectedChecks.Select(x => x.Id)).ToList();
                if (missingIds.Any())
                    throw new Exception($"Invalid check(s): {string.Join(", ", missingIds)}");

                foreach (var check in selectedChecks)
                {
                    check.CheckStatusId = CheckStatusEnumeration.Collected.Value;
                    check.CheckCollectionId = model.Id;

                    model.CheckStatusHistories.Add(new CheckStatusHistoryModel
                    {
                        TransactionDate = model.CollectionDate,
                        Note = $"Check collected on {model.CollectionDate:dd/MM/yyyy}",
                        CheckStatusFromId = CheckStatusEnumeration.Deposited.Value,
                        CheckStatusToId = CheckStatusEnumeration.Collected.Value,
                        CheckTreasuryVoucherId = check.Id,
                        CheckCollectionId = model.Id
                    });
                }

                model.CheckTreasuryVouchers = selectedChecks;

                var result = await _context.Set<CheckCollectionModel>().AddAsync(model);
                await _context.SaveChangesAsync();
                return result.Entity;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public override async Task<CheckCollectionModel?> UpdateAsync(CheckCollectionModel? model)
        {
            if (model == null)
                throw new Exception("Model cannot be null");
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var spec = new RepositorySpecifications<CheckCollectionModel>
                {
                    Includes = x => x
                        .Include(d => d.CheckTreasuryVouchers)
                        .ThenInclude(c => c.CheckStatusHistories),
                    SearchValue = x => x.Id == model.Id,
                    IsTackable = true
                };

                var existingCollectionQuery = GetQueryable(spec);

                var existingCollection = await existingCollectionQuery.FirstOrDefaultAsync();
                if (existingCollection == null)
                    throw new Exception("Collection not found");
                var existingChecks = existingCollection.CheckTreasuryVouchers.ToList();
                var newCheckIds = model.CheckTreasuryVouchers.Select(c => c.Id).ToHashSet();
                var oldCheckIds = existingChecks.Select(c => c.Id).ToHashSet();

                // 1. Checks to be removed
                var removedChecks = existingChecks.Where(c => !newCheckIds.Contains(c.Id)).ToList();

                foreach (var check in removedChecks)
                {
                    check.CheckStatusId = CheckStatusEnumeration.Deposited.Value;
                    check.CheckCollectionId = null;

                    var collectionHistory = check.CheckStatusHistories
                        .FirstOrDefault(h => h.CheckCollectionId == existingCollection.Id && h.CheckStatusToId == CheckStatusEnumeration.Collected.Value);

                    if (collectionHistory != null)
                        _context.Set<CheckStatusHistoryModel>().Remove(collectionHistory);
                }

                // 2. Checks to be added
                var addedCheckIds = newCheckIds.Except(oldCheckIds).ToList();
                var newCheckSpecs = new RepositorySpecifications<CheckTreasuryVoucherModel>
                {
                    Includes = x => x.Include(xx => xx.CheckStatusHistories),
                    SearchValue = x => addedCheckIds.Contains(x.Id) && x.CheckStatusId == CheckStatusEnumeration.Deposited.Value,
                    IsTackable = true
                };
                var addedChecks = GetQueryable(newCheckSpecs);

                foreach (var check in addedChecks)
                {
                    check.CheckStatusId = CheckStatusEnumeration.Collected.Value;
                    check.CheckCollectionId = existingCollection.Id;

                    check.CheckStatusHistories.Add(new CheckStatusHistoryModel
                    {
                        TransactionDate = model.CollectionDate,
                        Note = $"Check collected on {model.CollectionDate:dd/MM/yyyy}",
                        CheckStatusFromId = CheckStatusEnumeration.Deposited.Value,
                        CheckStatusToId = CheckStatusEnumeration.Collected.Value,
                        CheckTreasuryVoucherId = check.Id,
                        CheckCollectionId = existingCollection.Id
                    });
                }

                // 3. Remaining checks that were not changed
                var retainedChecks = existingChecks.Where(c => newCheckIds.Contains(c.Id)).ToList();

                // 4. Final update: fully replace the list with only the updated list
                existingCollection.CheckTreasuryVouchers = retainedChecks.Concat(addedChecks).ToList();
                existingCollection.CollectionDate = model.CollectionDate;

                var result = _context.Set<CheckCollectionModel>().Update(existingCollection);
                await _context.SaveChangesAsync();
                return result.Entity;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }
    }
}
