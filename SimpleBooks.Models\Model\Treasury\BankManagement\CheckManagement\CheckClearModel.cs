﻿namespace SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement
{
    [Table("CheckClear")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<CheckClearModel>))]
    public partial class CheckClearModel : BaseModel
    {
        [CustomRequired]
        [DisplayName("Clear Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateTime ClearDate { get; set; }

        [CustomRequired]
        [DisplayName("Check Treasury Vouchers")]
        public virtual ICollection<CheckTreasuryVoucherModel> CheckTreasuryVouchers { get; set; } = new List<CheckTreasuryVoucherModel>();
        [DisplayName("Check Status Histories")]
        public virtual ICollection<CheckStatusHistoryModel> CheckStatusHistories { get; set; } = new List<CheckStatusHistoryModel>();
    }
}
