﻿import { dataGridViewTools } from '/js/dataGridViewTools.js';
import { customerTools } from '/js/business/Sales/customerTools.js';
import { paymentTermTools } from '/js/business/Treasury/paymentTermTools.js';

export class SalesOrderHandler {
    constructor(customers, paymentTerms, products) {
        this.customers = customers;
        this.paymentTerms = paymentTerms;
        this.products = products;
    }

    onCustomerChanged(customerId) {
        const customer = this.customers?.$values?.find(c => c.Id == customerId);
        if (customer) {
            customerTools.updateCustomer(customer);
        }
    }

    onPaymentTermChanged(paymentTermId) {
        const term = this.paymentTerms?.$values?.find(t => t.Id == paymentTermId);
        if (term) {
            paymentTermTools.updatePaymentTerm(term, "SalesOrderDueDate");
        }
    }

    onProductChanged(productId, index) {
        const product = this.products?.$values?.find(x => x.Id == productId);
        const productUnits = product?.ProductUnits;
        if (productUnits) {
            const row = document.querySelector(`#SalesOrderLinesDataGridView tr[data-row-index="${index}"]`);
            const select = row?.querySelector(`select[name$='.ProductUnitId']`);
            if (!select) return;

            select.innerHTML = '<option value=""></option>';
            productUnits.$values.forEach(sub => {
                const opt = document.createElement("option");
                opt.value = sub.ProductUnit.Id;
                opt.text = sub.ProductUnit.UnitName;
                select.appendChild(opt);
            });
        }
    }

    onUnitChanged(unitId, index) {
        const row = document.querySelector(`#SalesOrderLinesDataGridView tr[data-row-index="${index}"]`);
        if (!row) return;

        const productId = row.querySelector(`select[name$='.ProductId']`)?.value;
        const product = this.products?.$values?.find(x => x.Id == productId);
        const units = product?.ProductUnits?.$values;

        const selectedUnit = units?.find(x => x.ProductUnitId == unitId);
        if (selectedUnit) {
            const row = document.querySelector(`#SalesOrderLinesDataGridView tr[data-row-index="${index}"]`);
            if (!row) return;

            const price = row.querySelector(`input[name$='.Price']`);
            const unitQtyRatio = row.querySelector(`input[name$='.UnitQtyRatio']`);

            if (price) {
                price.value = selectedUnit.ProductUnitSalesPrice;
                price.dispatchEvent(new Event('change', { bubbles: true }));
            }

            if (unitQtyRatio) {
                unitQtyRatio.value = selectedUnit.ProductUnitRatio;
                unitQtyRatio.dispatchEvent(new Event('change', { bubbles: true }));
            }
        }
    }

    onQuantityChanged(element) {
        const row = element.closest('tr');
        if (!row) return;

        const quantityInput = row.querySelector(`input[name$='.Quantity']`);
        const priceInput = row.querySelector(`input[name$='.Price']`);
        const amountInput = row.querySelector(`input[name$='.Amount']`);
        if (!quantityInput || !priceInput || !amountInput) return;

        const quantity = parseFloat(quantityInput.value) || 0;
        const price = parseFloat(priceInput.value) || 0;
        const amount = quantity * price;
        amountInput.value = isNaN(amount) ? "0.00" : amount.toFixed(2);
    }

    buildSalesOrderLineColumns(config) {
        const {
            products,
            includeHidden = false,
            hiddenValue = null
        } = config;

        const columns = [];

        if (includeHidden && hiddenValue) {
            columns.push({ type: 'hidden', name: 'SalesOrderId', value: hiddenValue });
        }

        columns.push(
            {
                type: 'select',
                generateSelect: (index, model) => `
                    <input type="hidden" name="${model}[${index}].UnitQtyRatio" id="${model}_${index}__UnitQtyRatio" />
                    <select class="form-select" name="${model}[${index}].ProductId"
                            id="${model}_${index}__ProductId"
                            onchange="handler.onProductChanged(this.value, ${index})"
                            data-val="true" data-val-required="Product Required.">
                        <option value=""></option>
                        ${products.$values.map(t => `<option value="${t.Id}">${t.ProductPurchasesDescription}</option>`).join("")}
                    </select>
                    <span class="text-danger" data-valmsg-for="${model}[${index}].ProductId" data-valmsg-replace="true"></span>`
            },
            {
                type: 'select',
                generateSelect: (index, model) => `
                    <select class="form-select" name="${model}[${index}].ProductUnitId"
                            id="${model}_${index}__ProductUnitId"
                            onchange="handler.onUnitChanged(this.value, ${index})"
                            data-val="true" data-val-required="Product Unit Required.">
                        <option value=""></option>
                    </select>
                    <span class="text-danger" data-valmsg-for="${model}[${index}].ProductUnitId" data-valmsg-replace="true"></span>`
            },
            {
                type: 'input',
                name: 'Quantity',
                inputType: 'number',
                validationAttributes: 'data-val="true" data-val-required=" Quantity Required."',
                eventAttributes: 'onchange="handler.onQuantityChanged(this)"'
            },
            {
                type: 'input',
                name: 'Price',
                inputType: 'number',
                validationAttributes: `data-val="true" data-val-required="Price Required."`,
                eventAttributes: 'onchange="handler.onQuantityChanged(this)"'
            },
            {
                type: 'input',
                name: 'Amount',
                inputType: 'number',
                validationAttributes: `data-val="true" data-val-required="Amount Required."`,
                isReadOnly: true
            },
            {
                type: 'button',
                label: 'Remove'
            }
        );

        return columns;
    }

    addRow(config) {
        const {
            products,
            modelName = "SalesOrderLines",
            tableId = "SalesOrderLinesDataGridView",
            includeHidden = false,
            hiddenValues = null
        } = config;

        const columns = this.buildSalesOrderLineColumns({
            products,
            includeHidden,
            hiddenValues
        });

        dataGridViewTools.addRowToDataGridView(modelName, tableId, columns);
    }

    addProductRow() {
        this.addRow({
            products: this.products,
            modelName: "SalesOrderLines",
            tableId: "SalesOrderLinesDataGridView",
            includeHidden: this.includeHidden,
            hiddenValues: this.includeHidden && this.hiddenValue ? { ProductId: this.hiddenValue.value } : null
        });
    }
}
