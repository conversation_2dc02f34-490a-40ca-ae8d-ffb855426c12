﻿namespace SimpleBooks.Models.Model.Purchases
{
    [Table("VendorType")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<VendorTypeModel>))]
    public class VendorTypeModel : BaseWithoutTrackingModel
    {
        [CustomRequired]
        [DisplayName("Vendor Type Name")]
        public string VendorTypeName { get; set; }

        public virtual ICollection<VendorModel> Vendors { get; set; } = new List<VendorModel>();
        public virtual ICollection<BillModel> Bills { get; set; } = new List<BillModel>();
        public virtual ICollection<BillReturnModel> BillReturns { get; set; } = new List<BillReturnModel>();
        public virtual ICollection<PurchaseOrderModel> PurchaseOrders { get; set; } = new List<PurchaseOrderModel>();
    }
}
