﻿namespace SimpleBooks.Models.ModelMapper.Treasury.BankManagement.CheckManagement
{
    public static class CheckClearMapper
    {
        public static CreateCheckClearViewModel ToCreateDto(this CheckClearModel entity)
        {
            CreateCheckClearViewModel viewModel = new CreateCheckClearViewModel()
            {
                ClearDate = entity.ClearDate,
                SelectedCheckTreasuryVouchers = entity.CheckTreasuryVouchers.Select(x => x.Id).ToList(),
            };
            return viewModel;
        }

        public static CheckClearModel ToEntity(this CreateCheckClearViewModel entity)
        {
            CheckClearModel model = new CheckClearModel()
            {
                ClearDate = entity.ClearDate,
                CheckTreasuryVouchers = entity.SelectedCheckTreasuryVouchers.Select(x => new CheckTreasuryVoucherModel()
                {
                    Id = x,
                }).ToList(),
            };
            return model;
        }

        public static UpdateCheckClearViewModel ToUpdateDto(this CheckClearModel entity)
        {
            UpdateCheckClearViewModel viewModel = new UpdateCheckClearViewModel()
            {
                Id = entity.Id,
                ClearDate = entity.ClearDate,
                SelectedCheckTreasuryVouchers = entity.CheckTreasuryVouchers.Select(x => x.Id).ToList(),
            };
            return viewModel;
        }

        public static CheckClearModel ToEntity(this UpdateCheckClearViewModel entity)
        {
            CheckClearModel model = new CheckClearModel()
            {
                Id = entity.Id,
                ClearDate = entity.ClearDate,
                CheckTreasuryVouchers = entity.SelectedCheckTreasuryVouchers.Select(x => new CheckTreasuryVoucherModel()
                {
                    Id = x,
                }).ToList(),
            };
            return model;
        }
    }
}
