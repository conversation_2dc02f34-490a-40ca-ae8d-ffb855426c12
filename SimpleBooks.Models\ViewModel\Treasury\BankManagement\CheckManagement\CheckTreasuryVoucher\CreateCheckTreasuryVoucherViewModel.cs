﻿namespace SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckTreasuryVoucher
{
    public class CreateCheckTreasuryVoucherViewModel : CreateTreasuryVoucherViewModel, IEntityMapper<CheckTreasuryVoucherModel, CreateCheckTreasuryVoucherViewModel>
    {
        [CustomRequired]
        [DisplayName("Check Number")]
        public string CheckNumber { get; set; }
        [CustomRequired]
        [DisplayName("Due Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateTime DueDate { get; set; }
        [DisplayName("Issuer Name")]
        public string IssuerName { get; set; } // For received checks
        [DisplayName("Bearer Name")]
        public string BearerName { get; set; } // For issued checks
        [CustomRequired]
        [DisplayName("Check Status")]
        public Ulid CheckStatusId { get; set; } = CheckStatusEnumeration.Unknown.Value;

        [DisplayName("Check Vault")]
        public Ulid? CheckVaultId { get; set; }
        [DisplayName("Check Vault Location")]
        public Ulid? CheckVaultLocationId { get; set; }
        [DisplayName("Bank")]
        public Ulid? BankId { get; set; }
        [DisplayName("Bank Account")]
        public Ulid? BankAccountId { get; set; }

        [DisplayName("Check Status Histories")]
        public IList<CreateCheckStatusHistoryViewModel> CheckStatusHistories { get; set; } = new List<CreateCheckStatusHistoryViewModel>();

        public CreateCheckTreasuryVoucherViewModel ToDto(CheckTreasuryVoucherModel entity) => entity.ToCreateDto();

        public CheckTreasuryVoucherModel ToEntity() => CheckTreasuryVoucherMapper.ToEntity(this);
    }
}
