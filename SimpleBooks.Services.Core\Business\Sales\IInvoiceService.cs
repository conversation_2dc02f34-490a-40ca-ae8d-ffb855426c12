﻿namespace SimpleBooks.Services.Core.Business.Sales
{
    public interface IInvoiceService : ISimpleBooksBaseService<InvoiceModel, IndexInvoiceViewModel, CreateInvoiceViewModel, UpdateInvoiceViewModel>
    {
        Task<ServiceResult<IEnumerable<CustomerDto>>> SelectiveCustomerListAsync();
        Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveCustomerTypeListAsync();
        Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveCustomerRepListAsync();
        Task<ServiceResult<IEnumerable<PaymentTermDto>>> SelectivePaymentTermListAsync();
        Task<ServiceResult<IEnumerable<ProductModel>>> SelectiveProductListAsync();
        Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveUnitListAsync();
        Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveStoreListAsync();
        Task<ServiceResult<IEnumerable<TaxTypeDto>>> TaxTypeListAsync();
        Task<ServiceResult<IEnumerable<TaxSubTypeDto>>> TaxSubTypeListAsync();
        Task<ServiceResult<IEnumerable<OpenSalesOrderDto>>> GetOpenSalesOrdersByCustomerAsync(Ulid customerId);
        Task<ServiceResult<IEnumerable<OpenSalesOrderLineDto>>> GetOpenSalesOrderLinesAsync(Ulid salesOrderId);
    }
}
