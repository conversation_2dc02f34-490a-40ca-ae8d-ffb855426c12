﻿namespace SimpleBooks.WEB.ViewModels.Treasury.BankManagement.CheckManagement.CheckTreasuryVoucher
{
    public class UpdateCheckTreasuryVoucherFormViewModel : UpdateCheckTreasuryVoucherViewModel
    {
        public IEnumerable<BeneficiaryTypeEnumeration> BeneficiaryTypes { get; set; } = Enumerable.Empty<BeneficiaryTypeEnumeration>();
        public IEnumerable<SelectListItem> Vendors { get; set; } = Enumerable.Empty<SelectListItem>();
        public IEnumerable<SelectListItem> Customers { get; set; } = Enumerable.Empty<SelectListItem>();
        public IEnumerable<SelectListItem> Employees { get; set; } = Enumerable.Empty<SelectListItem>();
        public IEnumerable<TransactionTypeEnumeration> TransactionTypes { get; set; } = Enumerable.Empty<TransactionTypeEnumeration>();
        public IEnumerable<SelectListItem> CheckVaults { get; set; } = Enumerable.Empty<SelectListItem>();
        public IEnumerable<CheckVaultLocationModel> CheckVaultLocations { get; set; } = Enumerable.Empty<CheckVaultLocationModel>();
        public IEnumerable<SelectListItem> Banks { get; set; } = Enumerable.Empty<SelectListItem>();
        public IEnumerable<BankAccountModel> BankAccounts { get; set; } = Enumerable.Empty<BankAccountModel>();
        public IEnumerable<TreasuryLineTypeEnumeration> TreasuryLineTypes { get; set; } = Enumerable.Empty<TreasuryLineTypeEnumeration>();
        public IEnumerable<SelectListItem> Expenses { get; set; } = Enumerable.Empty<SelectListItem>();
        public IEnumerable<SelectListItem> Bills { get; set; } = Enumerable.Empty<SelectListItem>();
        public IEnumerable<SelectListItem> BillReturns { get; set; } = Enumerable.Empty<SelectListItem>();
        public IEnumerable<SelectListItem> Invoices { get; set; } = Enumerable.Empty<SelectListItem>();
        public IEnumerable<SelectListItem> InvoiceReturns { get; set; } = Enumerable.Empty<SelectListItem>();
    }
}
