﻿namespace SimpleBooks.Models.Enumerations
{
    public class ActionStatusTypeEnumeration : UlidEnumeration<ActionStatusTypeEnumeration>
    {
        public static readonly ActionStatusTypeEnumeration Open = new ActionStatusTypeEnumeration(Ulid.Parse("01K0SJ1A7EQQ3H83M34MXAV5N8"), "Open");
        public static readonly ActionStatusTypeEnumeration InProgress = new ActionStatusTypeEnumeration(Ulid.Parse("01K0SJ1A7ERE028WDJ62Q63VKM"), "In Progress");
        public static readonly ActionStatusTypeEnumeration Cleared = new ActionStatusTypeEnumeration(Ulid.Parse("01K0SJ1A7EKBFJC3DGCT6D0FYH"), "Cleared");
        public static readonly ActionStatusTypeEnumeration Closed = new ActionStatusTypeEnumeration(Ulid.Parse("01K0SJ1A7FKZFW98W9NPY9Y2EH"), "Closed");

        private ActionStatusTypeEnumeration(Ulid key, string value) : base(key, value)
        {
        }

        public static List<ActionStatusTypeEnumeration> ActionStatusTypeEnumerations
        {
            get => new List<ActionStatusTypeEnumeration>
            {
                Open, InProgress, Cleared, Closed,
            };
        }
    }
}
