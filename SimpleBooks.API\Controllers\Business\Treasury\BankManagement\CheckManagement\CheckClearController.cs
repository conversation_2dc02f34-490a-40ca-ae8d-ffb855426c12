﻿namespace SimpleBooks.API.Controllers.Business.Treasury.BankManagement.CheckManagement
{
    public class CheckClearController : BaseBusinessController<CheckClearModel, CheckClearModel, CreateCheckClearViewModel, UpdateCheckClearViewModel>
    {
        private readonly ICheckClearService _checkClearService;

        public CheckClearController(ICheckClearService checkClearService) : base(checkClearService)
        {
            _checkClearService = checkClearService;
        }
    }
}
