﻿namespace SimpleBooks.Services.Server.Business.Treasury.BankManagement.CheckManagement
{
    public class CheckTreasuryVoucherService : SimpleBooksBaseService<CheckTreasuryVoucherModel, CheckTreasuryVoucherModel, CreateCheckTreasuryVoucherViewModel, UpdateCheckTreasuryVoucherViewModel>, ICheckTreasuryVoucherService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IVendorService _vendorService;
        private readonly ICustomerService _customerService;
        private readonly IEmployeeService _employeeService;
        private readonly ICheckVaultService _checkVaultService;
        private readonly ICheckVaultLocationService _checkVaultLocationService;
        private readonly IBankService _bankService;
        private readonly IBankAccountService _bankAccountService;
        private readonly IExpensesService _expensesService;
        private readonly IBillService _billService;
        private readonly IBillReturnService _billReturnService;
        private readonly IInvoiceService _invoiceService;
        private readonly IInvoiceReturnService _invoiceReturnService;

        public CheckTreasuryVoucherService(
            IAuthenticationValidationService authenticationValidationService,
            IUnitOfWork unitOfWork,
            IVendorService vendorService,
            ICustomerService customerService,
            IEmployeeService employeeService,
            ICheckVaultService checkVaultService,
            ICheckVaultLocationService checkVaultLocationService,
            IBankService bankService,
            IBankAccountService bankAccountService,
            IExpensesService expensesService,
            IBillService billService,
            IBillReturnService billReturnService,
            IInvoiceService invoiceService,
            IInvoiceReturnService invoiceReturnService) : base(authenticationValidationService, unitOfWork.CheckTreasuryVoucher)
        {
            _unitOfWork = unitOfWork;
            _vendorService = vendorService;
            _customerService = customerService;
            _employeeService = employeeService;
            _checkVaultService = checkVaultService;
            _checkVaultLocationService = checkVaultLocationService;
            _bankService = bankService;
            _bankAccountService = bankAccountService;
            _expensesService = expensesService;
            _billService = billService;
            _billReturnService = billReturnService;
            _invoiceService = invoiceService;
            _invoiceReturnService = invoiceReturnService;
        }

        protected override Func<IQueryable<CheckTreasuryVoucherModel>, IIncludableQueryable<CheckTreasuryVoucherModel, object>>? Includes =>
            x => x
            .Include(xx => xx.TreasuryLines)
            .Include(xx => xx.CheckStatusHistories);

        public override void EditModelBeforeSave(CheckTreasuryVoucherModel model)
        {
            base.EditModelBeforeSave(model);

            foreach (var treasuryLine in model.TreasuryLines)
            {
                treasuryLine.CashTreasuryVoucherId = null;
                treasuryLine.CheckTreasuryVoucherId = model.Id;
                treasuryLine.BankTransferTreasuryVoucherId = null;
                if (treasuryLine.TreasuryLineTypeId == TreasuryLineTypeEnumeration.Expenses.Value)
                {
                    treasuryLine.BillId = null;
                    treasuryLine.BillReturnId = null;
                    treasuryLine.InvoiceId = null;
                    treasuryLine.InvoiceReturnId = null;
                }
                else if (treasuryLine.TreasuryLineTypeId == TreasuryLineTypeEnumeration.Bill.Value)
                {
                    treasuryLine.ExpensesId = null;
                    treasuryLine.BillReturnId = null;
                    treasuryLine.InvoiceId = null;
                    treasuryLine.InvoiceReturnId = null;
                }
                else if (treasuryLine.TreasuryLineTypeId == TreasuryLineTypeEnumeration.BillReturn.Value)
                {
                    treasuryLine.ExpensesId = null;
                    treasuryLine.BillId = null;
                    treasuryLine.InvoiceId = null;
                    treasuryLine.InvoiceReturnId = null;
                }
                else if (treasuryLine.TreasuryLineTypeId == TreasuryLineTypeEnumeration.Invoice.Value)
                {
                    treasuryLine.ExpensesId = null;
                    treasuryLine.BillId = null;
                    treasuryLine.BillReturnId = null;
                    treasuryLine.InvoiceReturnId = null;
                }
                else if (treasuryLine.TreasuryLineTypeId == TreasuryLineTypeEnumeration.InvoiceReturn.Value)
                {
                    treasuryLine.ExpensesId = null;
                    treasuryLine.BillId = null;
                    treasuryLine.BillReturnId = null;
                    treasuryLine.InvoiceId = null;
                }
            }
            foreach (var checkStatusHistory in model.CheckStatusHistories)
            {
                checkStatusHistory.Id = model.Id;
            }
            if (model.VoucherSerial == 0)
                model.VoucherSerial = _unitOfWork.CheckTreasuryVoucher.GetNextVoucherSerial(model.TransactionTypeId);
            if (model.TVID == 0)
                model.TVID = _unitOfWork.CheckTreasuryVoucher.GetNextTVID();
            if (model.TransactionTypeId == TransactionTypeEnumeration.TreasuryCheckIn.Value)
            {
                model.BankId = null;
                model.BankAccountId = null;
            }
            else if (model.TransactionTypeId == TransactionTypeEnumeration.TreasuryCheckOut.Value)
            {
                model.CheckVaultId = null;
                model.CheckVaultLocationId = null;
            }
            if (model.BeneficiaryTypeId == BeneficiaryTypeEnumeration.Vendor.Value)
            {
                model.CustomerId = null;
                model.EmployeeId = null;
            }
            else if (model.BeneficiaryTypeId == BeneficiaryTypeEnumeration.Customer.Value)
            {
                model.VendorId = null;
                model.EmployeeId = null;
            }
            else if (model.BeneficiaryTypeId == BeneficiaryTypeEnumeration.Employee.Value)
            {
                model.VendorId = null;
                model.CustomerId = null;
            }
            if (CheckStatusEnumeration.IsInitialCheckStatus(model.CheckStatusId))
                model.CheckStatusId = CheckStatusEnumeration.InitialCheckStatus(model.TransactionTypeId);
            if (model.CheckStatusHistories.Count == 0)
            {
                model.CheckStatusHistories.Add(new CheckStatusHistoryModel()
                {
                    TransactionDate = model.TransactionDate,
                    Note = "Initial status",
                    CheckStatusFromId = CheckStatusEnumeration.InitialCheckStatus(model.TransactionTypeId),
                    CheckStatusToId = CheckStatusEnumeration.InitialCheckStatus(model.TransactionTypeId),
                    CheckTreasuryVoucherId = model.Id,
                });
            }
            else if (model.CheckStatusHistories.Count == 1)
            {
                // Ensure the first status is the initial status
                if (CheckStatusEnumeration.IsInitialCheckStatus(model.CheckStatusHistories.First().CheckStatusToId))
                {
                    model.CheckStatusHistories.First().CheckStatusToId = CheckStatusEnumeration.InitialCheckStatus(model.TransactionTypeId);
                }
            }
        }

        public override async void ValidateEntity(CheckTreasuryVoucherModel model, bool isEdit)
        {
            base.ValidateEntity(model, isEdit);

            if (model.TreasuryLines.Count == 0)
                throw new ValidationException("Treasury lines cannot be empty.");
            if (model.Amount != model.TreasuryLines.Sum(x => x.Amount))
                throw new ValidationException("The total amount of treasury lines must match the voucher amount.");
            if (model.TreasuryLines.Any(x => x.Amount <= 0))
                throw new ValidationException("Treasury line amounts must be greater than zero.");
            if (model.Amount <= 0)
                throw new ValidationException("Voucher amount must be greater than zero.");
            if (model.VendorId is null &&
                model.CustomerId is null &&
                model.EmployeeId is null)
                throw new ValidationException("At least one beneficiary must be selected.");
            if (model.CheckNumber is null)
                throw new ValidationException("Check number must be provided for check transactions.");
            if (model.IssuerName is null)
                throw new ValidationException("Issuer name must be provided for check transactions.");
            if (model.BearerName is null)
                throw new ValidationException("Bearer name must be provided for check transactions.");
            if (model.CheckStatusHistories.Count == 0)
                throw new ValidationException("Check status histories cannot be empty for check transactions.");
            foreach (var treasuryLine in model.TreasuryLines)
                if (treasuryLine.ExpensesId is null &&
                    treasuryLine.BillId is null &&
                    treasuryLine.BillReturnId is null &&
                    treasuryLine.InvoiceId is null &&
                    treasuryLine.InvoiceReturnId is null)
                    throw new ValidationException("Each treasury line must have at least one of Expenses, Bill, Bill Return, Invoice, or Invoice Return selected.");
            if (model.CheckVaultId is null &&
                model.CheckVaultLocationId is null &&
                model.BankId is null &&
                model.BankAccountId is null)
                throw new ValidationException("At least one of Check Vault, Check Vault Location, Bank, or Bank Account must be selected for check transactions.");
            if (model.CheckStatusHistories.Count > 1)
            {
                var orignalCheck = await _unitOfWork.CheckTreasuryVoucher.GetAsync(new RepositorySpecifications<CheckTreasuryVoucherModel>
                {
                    SearchValue = x => x.CheckNumber == model.CheckNumber && x.Id != model.Id,
                    IsTackable = false,
                });
                if (orignalCheck != null)
                {
                    if (orignalCheck.TransactionTypeId != model.TransactionTypeId)
                    {
                        var orignalTransactionType = TransactionTypeEnumeration.FromValue(orignalCheck.TransactionTypeId);
                        var newTransactionType = TransactionTypeEnumeration.FromValue(model.TransactionTypeId);
                        var transactions = model.CheckStatusHistories.Select(x => CheckStatusEnumeration.FromValue(x.CheckStatusToId));
                        throw new ValidationException($"Can't change the check voucher type from {orignalTransactionType.Name} to {newTransactionType.Name} becouse it related to other transactions like {string.Join(',', transactions.Select(x => x.Name))}.");
                    }
                }
            }
        }

        public override CheckTreasuryVoucherModel SetEntity(CheckTreasuryVoucherModel model, CheckTreasuryVoucherModel entity)
        {
            int serial = entity.VoucherSerial;
            int tvid = entity.TVID;

            entity = base.SetEntity(model, entity);

            entity.VoucherSerial = serial;
            entity.TVID = tvid;
            return entity;
        }

        public async Task<ServiceResult<IEnumerable<BeneficiaryTypeEnumeration>>> SelectiveBeneficiaryTypeListAsync() => await Task.FromResult(BeneficiaryTypeEnumeration.BeneficiaryTypeEnumerations);
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveVendorListAsync() => await _vendorService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveCustomerListAsync() => await _customerService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveEmployeeListAsync() => await _employeeService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<TransactionTypeEnumeration>>> SelectiveTransactionTypeListAsync() => await Task.FromResult(TransactionTypeEnumeration.TreasuryCheckTransactionTypeEnumerations);
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveCheckVaultListAsync() => await _checkVaultService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<CheckVaultLocationModel>>> SelectiveCheckVaultLocationListAsync() => await _checkVaultLocationService.GetAllAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveBankListAsync() => await _bankService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<BankAccountModel>>> SelectiveBankAccountListAsync() => await _bankAccountService.GetAllAsync();
        public async Task<ServiceResult<IEnumerable<TreasuryLineTypeEnumeration>>> SelectiveTreasuryLineTypeListAsync() => await Task.FromResult(TreasuryLineTypeEnumeration.TreasuryLineTypeEnumerations);
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveExpenseListAsync() => await _expensesService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveBillListAsync() => await _billService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveBillReturnListAsync() => await _billReturnService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveInvoiceListAsync() => await _invoiceService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveInvoiceReturnListAsync() => await _invoiceReturnService.GetSelectListAsync();

        public async Task<ServiceResult<IEnumerable<TreasuryVoucherDto>>> SelectiveCheckTreasuryVoucherDtoListAsync() => (await GetAllAsync()).Data?.Select(x => new TreasuryVoucherDto()
        {
            Id = x.Id,
            TransactionSerial = x.VoucherSerial,
            BeneficiaryType = BeneficiaryTypeEnumeration.FromValue(x.BeneficiaryTypeId).Name,
            TreasuryVoucherJson = JsonConvert.SerializeObject(x),
        }).ToList() ?? new List<TreasuryVoucherDto>();

        public async Task<ServiceResult<IEnumerable<CheckTreasuryVoucherModel>>> SelectiveCheckTreasuryVouchersAsync(Ulid checkStatusId)
        {
            RepositorySpecifications<CheckTreasuryVoucherModel> repositorySpecifications = new RepositorySpecifications<CheckTreasuryVoucherModel>()
            {
                SearchValue = x => x.CheckStatusId == checkStatusId,
                IsTackable = false,
            };
            var result = await _unitOfWork.CheckTreasuryVoucher.GetAllAsync(repositorySpecifications);
            return result;
        }

        public async Task<ServiceResult<IEnumerable<CheckTreasuryVoucherModel>>> SelectiveDepositCheckTreasuryVouchersAsync()
        {
            RepositorySpecifications<CheckTreasuryVoucherModel> repositorySpecifications = new RepositorySpecifications<CheckTreasuryVoucherModel>()
            {
                SearchValue = x => x.CheckStatusId == CheckStatusEnumeration.Deposited.Value || x.CheckStatusId == CheckStatusEnumeration.Returned.Value,
                IsTackable = false,
            };
            var result = await _unitOfWork.CheckTreasuryVoucher.GetAllAsync(repositorySpecifications);
            return result;
        }

        public async Task<ServiceResult<string>> GetByIdJsonAsync(Ulid id)
        {
            RepositorySpecifications<CheckTreasuryVoucherModel> repositorySpecifications = new RepositorySpecifications<CheckTreasuryVoucherModel>()
            {
                SearchValue = x => x.Id == id,
                IsTackable = false,
            };
            if (Includes is not null)
                repositorySpecifications.Includes = Includes;
            var model = await _unitOfWork.CheckTreasuryVoucher.GetAsync(repositorySpecifications);
            if (model is null)
                return string.Empty;

            JsonSerializerSettings jsonSerializerSettings = new JsonSerializerSettings()
            {
                ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                PreserveReferencesHandling = PreserveReferencesHandling.Objects,
            };
            string json = JsonConvert.SerializeObject(model, jsonSerializerSettings);

            return json;
        }
    }
}
