﻿namespace SimpleBooks.Models.Model.Warehouse
{
    [Table("Product")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<ProductModel>))]
    public class ProductModel : BaseWithTrackingModel
    {
        [CustomRequired]
        [DisplayName("Product Id")]
        public string ProductId { get; set; }
        [CustomRequired]
        [DisplayName("Product Name")]
        public string ProductName { get; set; }
        [CustomRequired]
        [DisplayName("Product Purchases Description")]
        public string ProductPurchasesDescription { get; set; }
        [CustomRequired]
        [DisplayName("Product Sales Description")]
        public string ProductSalesDescription { get; set; }

        [DisplayName("Product Type")]
        public Ulid? ProductTypeId { get; set; }
        public virtual ProductTypeModel? ProductType { get; set; }

        [DisplayName("Product Category")]
        public Ulid? ProductCategoryId { get; set; }
        public virtual ProductCategoryModel? ProductCategory { get; set; }

        [DisplayName("Product Units")]
        public virtual ICollection<ProductUnitModel> ProductUnits { get; set; } = new List<ProductUnitModel>();
        [DisplayName("Product Taxes")]
        public virtual ICollection<ProductTaxModel> ProductTaxes { get; set; } = new List<ProductTaxModel>();
        [DisplayName("Inventories")]
        public virtual ICollection<InventoryModel> Inventories { get; set; } = new List<InventoryModel>();
        [DisplayName("Purchase Order Lines")]
        public virtual ICollection<PurchaseOrderLineModel> PurchaseOrderLines { get; set; } = new List<PurchaseOrderLineModel>();
        [DisplayName("Sales Order Lines")]
        public virtual ICollection<SalesOrderLineModel> SalesOrderLines { get; set; } = new List<SalesOrderLineModel>();
    }
}
