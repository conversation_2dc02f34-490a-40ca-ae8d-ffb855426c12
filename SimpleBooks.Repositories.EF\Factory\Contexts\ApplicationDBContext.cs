﻿namespace SimpleBooks.Repositories.EF.Factory.Contexts
{
    public enum DBContextType
    {
        MySQL,
        SQLite,
    }

    public class ApplicationDBContext : DbContext
    {
        protected ApplicationDBContext(DbContextOptions options) : base(options)
        {
        }

        #region HR
        public virtual DbSet<EmployeeModel> Employees { get; set; }
        #endregion

        #region Purchases
        public virtual DbSet<VendorModel> Vendors { get; set; }
        public virtual DbSet<VendorTypeModel> VendorTypes { get; set; }
        public virtual DbSet<BillModel> Bills { get; set; }
        public virtual DbSet<BillReturnModel> BillReturns { get; set; }
        public virtual DbSet<PurchaseOrderModel> PurchaseOrders { get; set; }
        public virtual DbSet<PurchaseOrderLineModel> PurchaseOrderLines { get; set; }
        #endregion

        #region Sales
        public virtual DbSet<CustomerModel> Customers { get; set; }
        public virtual DbSet<CustomerTypeModel> CustomerTypes { get; set; }
        public virtual DbSet<InvoiceModel> Invoices { get; set; }
        public virtual DbSet<InvoiceReturnModel> InvoiceReturns { get; set; }
        public virtual DbSet<SalesOrderModel> SalesOrders { get; set; }
        public virtual DbSet<SalesOrderLineModel> SalesOrderLines { get; set; }
        #endregion

        #region Treasury
        public virtual DbSet<BankTransferTreasuryVoucherModel> BankTransferTreasuryVouchers { get; set; }
        public virtual DbSet<BankAccountModel> BankAccounts { get; set; }
        public virtual DbSet<BankModel> Banks { get; set; }
        public virtual DbSet<CashTreasuryVoucherModel> CashTreasuryVouchers { get; set; }
        public virtual DbSet<DrawerLocationModel> DrawerLocations { get; set; }
        public virtual DbSet<DrawerModel> Drawers { get; set; }
        public virtual DbSet<ExpensesModel> Expenses { get; set; }
        public virtual DbSet<PaymentTermModel> PaymentTerms { get; set; }
        public virtual DbSet<TreasuryLineModel> TreasuryLines { get; set; }
        public virtual DbSet<CheckClearModel> CheckClears { get; set; }
        public virtual DbSet<CheckCollectionModel> CheckCollections { get; set; }
        public virtual DbSet<CheckDepositModel> CheckDeposits { get; set; }
        public virtual DbSet<CheckRejectModel> CheckRejects { get; set; }
        public virtual DbSet<CheckReturnModel> CheckReturns { get; set; }
        public virtual DbSet<CheckStatusHistoryModel> CheckStatusHistories { get; set; }
        public virtual DbSet<CheckTreasuryVoucherModel> CheckTreasuryVouchers { get; set; }
        public virtual DbSet<CheckVaultLocationModel> CheckVaultLocations { get; set; }
        public virtual DbSet<CheckVaultModel> CheckVaults { get; set; }
        #endregion

        #region Tax
        public virtual DbSet<TaxTypeModel> TaxTypes { get; set; }
        public virtual DbSet<TaxSubTypeModel> TaxSubTypes { get; set; }
        #endregion

        #region User
        public virtual DbSet<AuditModel> Audits { get; set; }
        public virtual DbSet<RefreshTokenModel> RefreshTokens { get; set; }
        public virtual DbSet<ScreensAccessProfileModel> ScreensAccessProfiles { get; set; }
        public virtual DbSet<SettingModel> Settings { get; set; }
        public virtual DbSet<UserModel> Users { get; set; }
        #endregion

        #region Warehouse
        public virtual DbSet<InventoryModel> Inventories { get; set; }
        public virtual DbSet<InventoryTaxModel> InventoryTaxes { get; set; }
        public virtual DbSet<ProductCategoryModel> ProductCategories { get; set; }
        public virtual DbSet<ProductModel> Products { get; set; }
        public virtual DbSet<ProductTaxModel> ProductTaxes { get; set; }
        public virtual DbSet<ProductTypeModel> ProductTypes { get; set; }
        public virtual DbSet<ProductUnitModel> ProductUnits { get; set; }
        public virtual DbSet<StoreModel> Stores { get; set; }
        public virtual DbSet<UnitModel> Units { get; set; }
        #endregion

        public virtual DbSet<TransactionTypeModel> TransactionTypes { get; set; }

        protected override void ConfigureConventions(ModelConfigurationBuilder configurationBuilder)
        {
            configurationBuilder.Properties<decimal>().HavePrecision(18, 5);
            configurationBuilder.Properties<Ulid>().HaveConversion<UlidToBytesConverter>();

            base.ConfigureConventions(configurationBuilder);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfigurationsFromAssembly(typeof(ApplicationDBContext).Assembly);
            modelBuilder.Ignore<BaseWithTrackingModel>();
            modelBuilder.Ignore<BaseWithoutTrackingModel>();
            modelBuilder.Ignore<TreasuryVoucherModel>();

            base.OnModelCreating(modelBuilder);
        }
    }
}
