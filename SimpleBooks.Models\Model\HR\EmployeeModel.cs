﻿namespace SimpleBooks.Models.Model.HR
{
    [Table("Employee")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<EmployeeModel>))]
    public class EmployeeModel : BaseWithTrackingModel
    {
        [CustomRequired]
        [DisplayName("Employee Name")]
        public string EmployeeName { get; set; }
        [EmailAddress]
        [CustomRequired]
        [DisplayName("Employee E-Mail")]
        public string EmployeeEMail { get; set; }
        [Phone]
        [CustomRequired]
        [DisplayName("Employee Phone")]
        public string EmployeePhone { get; set; }
        [CustomRequired]
        [DisplayName("Employee Is Rep")]
        public bool EmployeeIsRep { get; set; }

        [DisplayName("User")]
        public Ulid? UserId { get; set; }
        public virtual UserModel? User { get; set; }

        public virtual ICollection<CustomerModel> Customers { get; set; } = new List<CustomerModel>();
        public virtual ICollection<InvoiceModel> Invoices { get; set; } = new List<InvoiceModel>();
        public virtual ICollection<InvoiceReturnModel> InvoiceReturns { get; set; } = new List<InvoiceReturnModel>();
        public virtual ICollection<SalesOrderModel> SalesOrders { get; set; } = new List<SalesOrderModel>();
        [DisplayName("Cash Treasury Vouchers")]
        public virtual ICollection<CashTreasuryVoucherModel> CashTreasuryVouchers { get; set; } = new List<CashTreasuryVoucherModel>();
        [DisplayName("Check Treasury Vouchers")]
        public virtual ICollection<CheckTreasuryVoucherModel> CheckTreasuryVouchers { get; set; } = new List<CheckTreasuryVoucherModel>();
        [DisplayName("Bank Transfer Treasury Vouchers")]
        public virtual ICollection<BankTransferTreasuryVoucherModel> BankTransferTreasuryVouchers { get; set; } = new List<BankTransferTreasuryVoucherModel>();
    }
}
