﻿namespace SimpleBooks.Models.ViewModel.Purchases.PurchaseOrder
{
    public class UpdatePurchaseOrderViewModel : BaseUpdateViewModel, IEntityMapper<PurchaseOrderModel, UpdatePurchaseOrderViewModel>
    {
        [CustomRequired]
        [DisplayName("Purchase Order Id")]
        public string PurchaseOrderId { get; set; }
        [CustomRequired]
        [DisplayName("Purchase Order Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateOnly PurchaseOrderDate { get; set; }
        [CustomRequired]
        [DisplayName("Purchase Order Due Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateOnly PurchaseOrderDueDate { get; set; }

        [CustomRequired]
        [DisplayName("Vendor")]
        public Ulid VendorId { get; set; }
        [DisplayName("Vendor Type")]
        public Ulid? VendorTypeId { get; set; }
        [DisplayName("Payment Term")]
        public Ulid? PaymentTermId { get; set; }

        [CustomRequired]
        [DisplayName("Purchase Order Lines")]
        public IList<UpdatePurchaseOrderLineViewModel> PurchaseOrderLines { get; set; } = new List<UpdatePurchaseOrderLineViewModel>();

        public UpdatePurchaseOrderViewModel ToDto(PurchaseOrderModel entity) => entity.ToUpdateDto();

        public PurchaseOrderModel ToEntity() => PurchaseOrderMapper.ToEntity(this);
    }
}
