﻿namespace SimpleBooks.Models.ModelMapper.Sales
{
    public static class SalesOrderMapper
    {
        public static CreateSalesOrderViewModel ToCreateDto(this SalesOrderModel entity)
        {
            CreateSalesOrderViewModel viewModel = new CreateSalesOrderViewModel()
            {
                SalesOrderId = entity.SalesOrderId,
                SalesOrderDate = entity.SalesOrderDate,
                SalesOrderDueDate = entity.SalesOrderDueDate,
                CustomerId = entity.CustomerId,
                CustomerTypeId = entity.CustomerTypeId,
                CustomerRepId = entity.CustomerRepId,
                PaymentTermId = entity.PaymentTermId,
                SalesOrderLines = entity.SalesOrderLines.Select(i => i.ToCreateDto()).ToList(),
            };
            return viewModel;
        }

        public static SalesOrderModel ToEntity(this CreateSalesOrderViewModel entity)
        {
            SalesOrderModel model = new SalesOrderModel()
            {
                SalesOrderId = entity.SalesOrderId,
                SalesOrderDate = entity.SalesOrderDate,
                SalesOrderDueDate = entity.SalesOrderDueDate,
                CustomerId = entity.CustomerId,
                CustomerTypeId = entity.CustomerTypeId,
                CustomerRepId = entity.CustomerRepId,
                PaymentTermId = entity.PaymentTermId,
                SalesOrderLines = entity.SalesOrderLines.Select(i => i.ToEntity()).ToList(),
            };
            return model;
        }

        public static UpdateSalesOrderViewModel ToUpdateDto(this SalesOrderModel entity)
        {
            UpdateSalesOrderViewModel viewModel = new UpdateSalesOrderViewModel()
            {
                Id = entity.Id,
                SalesOrderId = entity.SalesOrderId,
                SalesOrderDate = entity.SalesOrderDate,
                SalesOrderDueDate = entity.SalesOrderDueDate,
                CustomerId = entity.CustomerId,
                CustomerTypeId = entity.CustomerTypeId,
                CustomerRepId = entity.CustomerRepId,
                PaymentTermId = entity.PaymentTermId,
                SalesOrderLines = entity.SalesOrderLines.Select(i => i.ToUpdateDto()).ToList(),
            };
            return viewModel;
        }

        public static SalesOrderModel ToEntity(this UpdateSalesOrderViewModel entity)
        {
            SalesOrderModel model = new SalesOrderModel()
            {
                Id = entity.Id,
                SalesOrderId = entity.SalesOrderId,
                SalesOrderDate = entity.SalesOrderDate,
                SalesOrderDueDate = entity.SalesOrderDueDate,
                CustomerId = entity.CustomerId,
                CustomerTypeId = entity.CustomerTypeId,
                CustomerRepId = entity.CustomerRepId,
                PaymentTermId = entity.PaymentTermId,
                SalesOrderLines = entity.SalesOrderLines.Select(i => i.ToEntity()).ToList(),
            };
            return model;
        }
    }
}
