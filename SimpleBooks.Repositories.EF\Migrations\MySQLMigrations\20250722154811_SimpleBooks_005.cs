﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace SimpleBooks.Repositories.EF.Migrations.MySQLMigrations
{
    /// <inheritdoc />
    public partial class SimpleBooks_005 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CheckVaultLocation_Check Vault_CheckVaultId",
                table: "CheckVaultLocation");

            migrationBuilder.DropForeignKey(
                name: "FK_TreasuryLine_TreasuryVoucher_TreasuryVoucherId",
                table: "TreasuryLine");

            migrationBuilder.DropTable(
                name: "TreasuryVoucher");

            migrationBuilder.DropTable(
                name: "Check Vault");

            migrationBuilder.DropTable(
                name: "TreasuryVoucherType");

            migrationBuilder.DropIndex(
                name: "IX_TreasuryLine_TreasuryVoucherId",
                table: "TreasuryLine");

            migrationBuilder.DropColumn(
                name: "TreasuryVoucherId",
                table: "TreasuryLine");

            migrationBuilder.AddColumn<byte[]>(
                name: "BankTransferTreasuryVoucherId",
                table: "TreasuryLine",
                type: "varbinary(16)",
                nullable: true);

            migrationBuilder.AddColumn<byte[]>(
                name: "CashTreasuryVoucherId",
                table: "TreasuryLine",
                type: "varbinary(16)",
                nullable: true);

            migrationBuilder.AddColumn<byte[]>(
                name: "CheckTreasuryVoucherId",
                table: "TreasuryLine",
                type: "varbinary(16)",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "BeneficiaryType",
                columns: table => new
                {
                    Id = table.Column<byte[]>(type: "varbinary(16)", nullable: false),
                    BeneficiaryTypeName = table.Column<string>(type: "longtext", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BeneficiaryType", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "CheckClear",
                columns: table => new
                {
                    Id = table.Column<byte[]>(type: "varbinary(16)", nullable: false),
                    ClearDate = table.Column<DateTime>(type: "Date", nullable: false),
                    CreatedBy = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    DeletedBy = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    ModifaiedBy = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    ModifaiedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    IsActive = table.Column<bool>(type: "tinyint(1)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CheckClear", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "CheckCollection",
                columns: table => new
                {
                    Id = table.Column<byte[]>(type: "varbinary(16)", nullable: false),
                    CollectionDate = table.Column<DateTime>(type: "Date", nullable: false),
                    CreatedBy = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    DeletedBy = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    ModifaiedBy = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    ModifaiedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    IsActive = table.Column<bool>(type: "tinyint(1)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CheckCollection", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "CheckDeposit",
                columns: table => new
                {
                    Id = table.Column<byte[]>(type: "varbinary(16)", nullable: false),
                    DepositDate = table.Column<DateTime>(type: "Date", nullable: false),
                    RefranceNumber = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    BankId = table.Column<byte[]>(type: "varbinary(16)", nullable: false),
                    BankAccountId = table.Column<byte[]>(type: "varbinary(16)", nullable: false),
                    CreatedBy = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    DeletedBy = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    ModifaiedBy = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    ModifaiedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    IsActive = table.Column<bool>(type: "tinyint(1)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CheckDeposit", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CheckDeposit_BankAccount_BankAccountId",
                        column: x => x.BankAccountId,
                        principalTable: "BankAccount",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckDeposit_Bank_BankId",
                        column: x => x.BankId,
                        principalTable: "Bank",
                        principalColumn: "Id");
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "CheckReject",
                columns: table => new
                {
                    Id = table.Column<byte[]>(type: "varbinary(16)", nullable: false),
                    RejectDate = table.Column<DateTime>(type: "Date", nullable: false),
                    RejectReason = table.Column<string>(type: "longtext", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    RefranceNumber = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatedBy = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    DeletedBy = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    ModifaiedBy = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    ModifaiedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    IsActive = table.Column<bool>(type: "tinyint(1)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CheckReject", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "CheckVault",
                columns: table => new
                {
                    Id = table.Column<byte[]>(type: "varbinary(16)", nullable: false),
                    CheckVaultName = table.Column<string>(type: "longtext", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CheckVault", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "BankTransferTreasuryVoucher",
                columns: table => new
                {
                    Id = table.Column<byte[]>(type: "varbinary(16)", nullable: false),
                    BankId = table.Column<byte[]>(type: "varbinary(16)", nullable: false),
                    BankAccountId = table.Column<byte[]>(type: "varbinary(16)", nullable: false),
                    TransactionDate = table.Column<DateTime>(type: "Date", nullable: false),
                    VoucherSerial = table.Column<int>(type: "int", nullable: false),
                    TVID = table.Column<int>(type: "int", nullable: false),
                    Amount = table.Column<decimal>(type: "decimal(18,5)", precision: 18, scale: 5, nullable: false),
                    Note = table.Column<string>(type: "longtext", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    RefranceNumber = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    TransactionTypeId = table.Column<byte[]>(type: "varbinary(16)", nullable: false),
                    BeneficiaryTypeId = table.Column<byte[]>(type: "varbinary(16)", nullable: false),
                    VendorId = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    CustomerId = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    EmployeeId = table.Column<byte[]>(type: "varbinary(16)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BankTransferTreasuryVoucher", x => x.Id);
                    table.ForeignKey(
                        name: "FK_BankTransferTreasuryVoucher_BankAccount_BankAccountId",
                        column: x => x.BankAccountId,
                        principalTable: "BankAccount",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_BankTransferTreasuryVoucher_Bank_BankId",
                        column: x => x.BankId,
                        principalTable: "Bank",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_BankTransferTreasuryVoucher_BeneficiaryType_BeneficiaryTypeId",
                        column: x => x.BeneficiaryTypeId,
                        principalTable: "BeneficiaryType",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_BankTransferTreasuryVoucher_Customer_CustomerId",
                        column: x => x.CustomerId,
                        principalTable: "Customer",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_BankTransferTreasuryVoucher_Employee_EmployeeId",
                        column: x => x.EmployeeId,
                        principalTable: "Employee",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_BankTransferTreasuryVoucher_TransactionType_TransactionTypeId",
                        column: x => x.TransactionTypeId,
                        principalTable: "TransactionType",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_BankTransferTreasuryVoucher_Vendor_VendorId",
                        column: x => x.VendorId,
                        principalTable: "Vendor",
                        principalColumn: "Id");
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "CashTreasuryVoucher",
                columns: table => new
                {
                    Id = table.Column<byte[]>(type: "varbinary(16)", nullable: false),
                    DrawerId = table.Column<byte[]>(type: "varbinary(16)", nullable: false),
                    DrawerLocationId = table.Column<byte[]>(type: "varbinary(16)", nullable: false),
                    TransactionDate = table.Column<DateTime>(type: "Date", nullable: false),
                    VoucherSerial = table.Column<int>(type: "int", nullable: false),
                    TVID = table.Column<int>(type: "int", nullable: false),
                    Amount = table.Column<decimal>(type: "decimal(18,5)", precision: 18, scale: 5, nullable: false),
                    Note = table.Column<string>(type: "longtext", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    RefranceNumber = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    TransactionTypeId = table.Column<byte[]>(type: "varbinary(16)", nullable: false),
                    BeneficiaryTypeId = table.Column<byte[]>(type: "varbinary(16)", nullable: false),
                    VendorId = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    CustomerId = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    EmployeeId = table.Column<byte[]>(type: "varbinary(16)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CashTreasuryVoucher", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CashTreasuryVoucher_BeneficiaryType_BeneficiaryTypeId",
                        column: x => x.BeneficiaryTypeId,
                        principalTable: "BeneficiaryType",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CashTreasuryVoucher_Customer_CustomerId",
                        column: x => x.CustomerId,
                        principalTable: "Customer",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CashTreasuryVoucher_DrawerLocation_DrawerLocationId",
                        column: x => x.DrawerLocationId,
                        principalTable: "DrawerLocation",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CashTreasuryVoucher_Drawer_DrawerId",
                        column: x => x.DrawerId,
                        principalTable: "Drawer",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CashTreasuryVoucher_Employee_EmployeeId",
                        column: x => x.EmployeeId,
                        principalTable: "Employee",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CashTreasuryVoucher_TransactionType_TransactionTypeId",
                        column: x => x.TransactionTypeId,
                        principalTable: "TransactionType",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CashTreasuryVoucher_Vendor_VendorId",
                        column: x => x.VendorId,
                        principalTable: "Vendor",
                        principalColumn: "Id");
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "CheckReturn",
                columns: table => new
                {
                    Id = table.Column<byte[]>(type: "varbinary(16)", nullable: false),
                    ReturnDate = table.Column<DateTime>(type: "Date", nullable: false),
                    RefranceNumber = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CheckVaultId = table.Column<byte[]>(type: "varbinary(16)", nullable: false),
                    CheckVaultLocationId = table.Column<byte[]>(type: "varbinary(16)", nullable: false),
                    CreatedBy = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    DeletedBy = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    ModifaiedBy = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    ModifaiedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    IsActive = table.Column<bool>(type: "tinyint(1)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CheckReturn", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CheckReturn_CheckVaultLocation_CheckVaultLocationId",
                        column: x => x.CheckVaultLocationId,
                        principalTable: "CheckVaultLocation",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckReturn_CheckVault_CheckVaultId",
                        column: x => x.CheckVaultId,
                        principalTable: "CheckVault",
                        principalColumn: "Id");
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "CheckTreasuryVoucher",
                columns: table => new
                {
                    Id = table.Column<byte[]>(type: "varbinary(16)", nullable: false),
                    CheckNumber = table.Column<string>(type: "longtext", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    DueDate = table.Column<DateTime>(type: "Date", nullable: false),
                    IssuerName = table.Column<string>(type: "longtext", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    BearerName = table.Column<string>(type: "longtext", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CheckStatusId = table.Column<byte[]>(type: "varbinary(16)", nullable: false),
                    CheckVaultId = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    CheckVaultLocationId = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    BankId = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    BankAccountId = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    CheckDepositId = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    CheckCollectionId = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    CheckRejectId = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    CheckReturnId = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    CheckClearId = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    TransactionDate = table.Column<DateTime>(type: "Date", nullable: false),
                    VoucherSerial = table.Column<int>(type: "int", nullable: false),
                    TVID = table.Column<int>(type: "int", nullable: false),
                    Amount = table.Column<decimal>(type: "decimal(18,5)", precision: 18, scale: 5, nullable: false),
                    Note = table.Column<string>(type: "longtext", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    RefranceNumber = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    TransactionTypeId = table.Column<byte[]>(type: "varbinary(16)", nullable: false),
                    BeneficiaryTypeId = table.Column<byte[]>(type: "varbinary(16)", nullable: false),
                    VendorId = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    CustomerId = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    EmployeeId = table.Column<byte[]>(type: "varbinary(16)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CheckTreasuryVoucher", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CheckTreasuryVoucher_BankAccount_BankAccountId",
                        column: x => x.BankAccountId,
                        principalTable: "BankAccount",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckTreasuryVoucher_Bank_BankId",
                        column: x => x.BankId,
                        principalTable: "Bank",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckTreasuryVoucher_BeneficiaryType_BeneficiaryTypeId",
                        column: x => x.BeneficiaryTypeId,
                        principalTable: "BeneficiaryType",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckTreasuryVoucher_CheckClear_CheckClearId",
                        column: x => x.CheckClearId,
                        principalTable: "CheckClear",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckTreasuryVoucher_CheckCollection_CheckCollectionId",
                        column: x => x.CheckCollectionId,
                        principalTable: "CheckCollection",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckTreasuryVoucher_CheckDeposit_CheckDepositId",
                        column: x => x.CheckDepositId,
                        principalTable: "CheckDeposit",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckTreasuryVoucher_CheckReject_CheckRejectId",
                        column: x => x.CheckRejectId,
                        principalTable: "CheckReject",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckTreasuryVoucher_CheckReturn_CheckReturnId",
                        column: x => x.CheckReturnId,
                        principalTable: "CheckReturn",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckTreasuryVoucher_CheckStatus_CheckStatusId",
                        column: x => x.CheckStatusId,
                        principalTable: "CheckStatus",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckTreasuryVoucher_CheckVaultLocation_CheckVaultLocationId",
                        column: x => x.CheckVaultLocationId,
                        principalTable: "CheckVaultLocation",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckTreasuryVoucher_CheckVault_CheckVaultId",
                        column: x => x.CheckVaultId,
                        principalTable: "CheckVault",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckTreasuryVoucher_Customer_CustomerId",
                        column: x => x.CustomerId,
                        principalTable: "Customer",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckTreasuryVoucher_Employee_EmployeeId",
                        column: x => x.EmployeeId,
                        principalTable: "Employee",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckTreasuryVoucher_TransactionType_TransactionTypeId",
                        column: x => x.TransactionTypeId,
                        principalTable: "TransactionType",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckTreasuryVoucher_Vendor_VendorId",
                        column: x => x.VendorId,
                        principalTable: "Vendor",
                        principalColumn: "Id");
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "CheckStatusHistory",
                columns: table => new
                {
                    Id = table.Column<byte[]>(type: "varbinary(16)", nullable: false),
                    TransactionDate = table.Column<DateTime>(type: "Date", nullable: false),
                    Note = table.Column<string>(type: "longtext", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CheckStatusFromId = table.Column<byte[]>(type: "varbinary(16)", nullable: false),
                    CheckStatusToId = table.Column<byte[]>(type: "varbinary(16)", nullable: false),
                    CheckTreasuryVoucherId = table.Column<byte[]>(type: "varbinary(16)", nullable: false),
                    CheckDepositId = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    CheckCollectionId = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    CheckRejectId = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    CheckReturnId = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    CheckClearId = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    CreatedBy = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    DeletedBy = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    ModifaiedBy = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    ModifaiedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    IsActive = table.Column<bool>(type: "tinyint(1)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CheckStatusHistory", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CheckStatusHistory_CheckClear_CheckClearId",
                        column: x => x.CheckClearId,
                        principalTable: "CheckClear",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckStatusHistory_CheckCollection_CheckCollectionId",
                        column: x => x.CheckCollectionId,
                        principalTable: "CheckCollection",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckStatusHistory_CheckDeposit_CheckDepositId",
                        column: x => x.CheckDepositId,
                        principalTable: "CheckDeposit",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckStatusHistory_CheckReject_CheckRejectId",
                        column: x => x.CheckRejectId,
                        principalTable: "CheckReject",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckStatusHistory_CheckReturn_CheckReturnId",
                        column: x => x.CheckReturnId,
                        principalTable: "CheckReturn",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckStatusHistory_CheckStatus_CheckStatusFromId",
                        column: x => x.CheckStatusFromId,
                        principalTable: "CheckStatus",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckStatusHistory_CheckStatus_CheckStatusToId",
                        column: x => x.CheckStatusToId,
                        principalTable: "CheckStatus",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CheckStatusHistory_CheckTreasuryVoucher_CheckTreasuryVoucher~",
                        column: x => x.CheckTreasuryVoucherId,
                        principalTable: "CheckTreasuryVoucher",
                        principalColumn: "Id");
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.InsertData(
                table: "BeneficiaryType",
                columns: new[] { "Id", "BeneficiaryTypeName" },
                values: new object[,]
                {
                    { new byte[] { 1, 150, 166, 244, 223, 62, 168, 117, 131, 54, 139, 240, 41, 241, 175, 198 }, "Vendor" },
                    { new byte[] { 1, 150, 166, 244, 253, 135, 224, 197, 11, 169, 248, 206, 236, 0, 52, 135 }, "Customer" },
                    { new byte[] { 1, 150, 166, 245, 9, 59, 54, 202, 156, 33, 131, 111, 169, 212, 130, 131 }, "Employee" }
                });

            migrationBuilder.InsertData(
                table: "CheckStatus",
                columns: new[] { "Id", "CheckStatusName", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "IsActive", "ModifaiedAt", "ModifaiedBy" },
                values: new object[] { new byte[] { 1, 152, 36, 25, 206, 161, 247, 233, 47, 93, 6, 121, 216, 73, 83, 36 }, "Collected", null, null, null, null, true, null, null });

            migrationBuilder.CreateIndex(
                name: "IX_TreasuryLine_BankTransferTreasuryVoucherId",
                table: "TreasuryLine",
                column: "BankTransferTreasuryVoucherId");

            migrationBuilder.CreateIndex(
                name: "IX_TreasuryLine_CashTreasuryVoucherId",
                table: "TreasuryLine",
                column: "CashTreasuryVoucherId");

            migrationBuilder.CreateIndex(
                name: "IX_TreasuryLine_CheckTreasuryVoucherId",
                table: "TreasuryLine",
                column: "CheckTreasuryVoucherId");

            migrationBuilder.CreateIndex(
                name: "IX_BankTransferTreasuryVoucher_BankAccountId",
                table: "BankTransferTreasuryVoucher",
                column: "BankAccountId");

            migrationBuilder.CreateIndex(
                name: "IX_BankTransferTreasuryVoucher_BankId",
                table: "BankTransferTreasuryVoucher",
                column: "BankId");

            migrationBuilder.CreateIndex(
                name: "IX_BankTransferTreasuryVoucher_BeneficiaryTypeId",
                table: "BankTransferTreasuryVoucher",
                column: "BeneficiaryTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_BankTransferTreasuryVoucher_CustomerId",
                table: "BankTransferTreasuryVoucher",
                column: "CustomerId");

            migrationBuilder.CreateIndex(
                name: "IX_BankTransferTreasuryVoucher_EmployeeId",
                table: "BankTransferTreasuryVoucher",
                column: "EmployeeId");

            migrationBuilder.CreateIndex(
                name: "IX_BankTransferTreasuryVoucher_TransactionTypeId",
                table: "BankTransferTreasuryVoucher",
                column: "TransactionTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_BankTransferTreasuryVoucher_VendorId",
                table: "BankTransferTreasuryVoucher",
                column: "VendorId");

            migrationBuilder.CreateIndex(
                name: "IX_CashTreasuryVoucher_BeneficiaryTypeId",
                table: "CashTreasuryVoucher",
                column: "BeneficiaryTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_CashTreasuryVoucher_CustomerId",
                table: "CashTreasuryVoucher",
                column: "CustomerId");

            migrationBuilder.CreateIndex(
                name: "IX_CashTreasuryVoucher_DrawerId",
                table: "CashTreasuryVoucher",
                column: "DrawerId");

            migrationBuilder.CreateIndex(
                name: "IX_CashTreasuryVoucher_DrawerLocationId",
                table: "CashTreasuryVoucher",
                column: "DrawerLocationId");

            migrationBuilder.CreateIndex(
                name: "IX_CashTreasuryVoucher_EmployeeId",
                table: "CashTreasuryVoucher",
                column: "EmployeeId");

            migrationBuilder.CreateIndex(
                name: "IX_CashTreasuryVoucher_TransactionTypeId",
                table: "CashTreasuryVoucher",
                column: "TransactionTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_CashTreasuryVoucher_VendorId",
                table: "CashTreasuryVoucher",
                column: "VendorId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckDeposit_BankAccountId",
                table: "CheckDeposit",
                column: "BankAccountId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckDeposit_BankId",
                table: "CheckDeposit",
                column: "BankId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckReturn_CheckVaultId",
                table: "CheckReturn",
                column: "CheckVaultId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckReturn_CheckVaultLocationId",
                table: "CheckReturn",
                column: "CheckVaultLocationId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckStatusHistory_CheckClearId",
                table: "CheckStatusHistory",
                column: "CheckClearId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckStatusHistory_CheckCollectionId",
                table: "CheckStatusHistory",
                column: "CheckCollectionId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckStatusHistory_CheckDepositId",
                table: "CheckStatusHistory",
                column: "CheckDepositId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckStatusHistory_CheckRejectId",
                table: "CheckStatusHistory",
                column: "CheckRejectId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckStatusHistory_CheckReturnId",
                table: "CheckStatusHistory",
                column: "CheckReturnId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckStatusHistory_CheckStatusFromId",
                table: "CheckStatusHistory",
                column: "CheckStatusFromId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckStatusHistory_CheckStatusToId",
                table: "CheckStatusHistory",
                column: "CheckStatusToId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckStatusHistory_CheckTreasuryVoucherId",
                table: "CheckStatusHistory",
                column: "CheckTreasuryVoucherId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckTreasuryVoucher_BankAccountId",
                table: "CheckTreasuryVoucher",
                column: "BankAccountId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckTreasuryVoucher_BankId",
                table: "CheckTreasuryVoucher",
                column: "BankId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckTreasuryVoucher_BeneficiaryTypeId",
                table: "CheckTreasuryVoucher",
                column: "BeneficiaryTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckTreasuryVoucher_CheckClearId",
                table: "CheckTreasuryVoucher",
                column: "CheckClearId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckTreasuryVoucher_CheckCollectionId",
                table: "CheckTreasuryVoucher",
                column: "CheckCollectionId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckTreasuryVoucher_CheckDepositId",
                table: "CheckTreasuryVoucher",
                column: "CheckDepositId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckTreasuryVoucher_CheckRejectId",
                table: "CheckTreasuryVoucher",
                column: "CheckRejectId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckTreasuryVoucher_CheckReturnId",
                table: "CheckTreasuryVoucher",
                column: "CheckReturnId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckTreasuryVoucher_CheckStatusId",
                table: "CheckTreasuryVoucher",
                column: "CheckStatusId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckTreasuryVoucher_CheckVaultId",
                table: "CheckTreasuryVoucher",
                column: "CheckVaultId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckTreasuryVoucher_CheckVaultLocationId",
                table: "CheckTreasuryVoucher",
                column: "CheckVaultLocationId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckTreasuryVoucher_CustomerId",
                table: "CheckTreasuryVoucher",
                column: "CustomerId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckTreasuryVoucher_EmployeeId",
                table: "CheckTreasuryVoucher",
                column: "EmployeeId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckTreasuryVoucher_TransactionTypeId",
                table: "CheckTreasuryVoucher",
                column: "TransactionTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_CheckTreasuryVoucher_VendorId",
                table: "CheckTreasuryVoucher",
                column: "VendorId");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckVaultLocation_CheckVault_CheckVaultId",
                table: "CheckVaultLocation",
                column: "CheckVaultId",
                principalTable: "CheckVault",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_TreasuryLine_BankTransferTreasuryVoucher_BankTransferTreasur~",
                table: "TreasuryLine",
                column: "BankTransferTreasuryVoucherId",
                principalTable: "BankTransferTreasuryVoucher",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_TreasuryLine_CashTreasuryVoucher_CashTreasuryVoucherId",
                table: "TreasuryLine",
                column: "CashTreasuryVoucherId",
                principalTable: "CashTreasuryVoucher",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_TreasuryLine_CheckTreasuryVoucher_CheckTreasuryVoucherId",
                table: "TreasuryLine",
                column: "CheckTreasuryVoucherId",
                principalTable: "CheckTreasuryVoucher",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CheckVaultLocation_CheckVault_CheckVaultId",
                table: "CheckVaultLocation");

            migrationBuilder.DropForeignKey(
                name: "FK_TreasuryLine_BankTransferTreasuryVoucher_BankTransferTreasur~",
                table: "TreasuryLine");

            migrationBuilder.DropForeignKey(
                name: "FK_TreasuryLine_CashTreasuryVoucher_CashTreasuryVoucherId",
                table: "TreasuryLine");

            migrationBuilder.DropForeignKey(
                name: "FK_TreasuryLine_CheckTreasuryVoucher_CheckTreasuryVoucherId",
                table: "TreasuryLine");

            migrationBuilder.DropTable(
                name: "BankTransferTreasuryVoucher");

            migrationBuilder.DropTable(
                name: "CashTreasuryVoucher");

            migrationBuilder.DropTable(
                name: "CheckStatusHistory");

            migrationBuilder.DropTable(
                name: "CheckTreasuryVoucher");

            migrationBuilder.DropTable(
                name: "BeneficiaryType");

            migrationBuilder.DropTable(
                name: "CheckClear");

            migrationBuilder.DropTable(
                name: "CheckCollection");

            migrationBuilder.DropTable(
                name: "CheckDeposit");

            migrationBuilder.DropTable(
                name: "CheckReject");

            migrationBuilder.DropTable(
                name: "CheckReturn");

            migrationBuilder.DropTable(
                name: "CheckVault");

            migrationBuilder.DropIndex(
                name: "IX_TreasuryLine_BankTransferTreasuryVoucherId",
                table: "TreasuryLine");

            migrationBuilder.DropIndex(
                name: "IX_TreasuryLine_CashTreasuryVoucherId",
                table: "TreasuryLine");

            migrationBuilder.DropIndex(
                name: "IX_TreasuryLine_CheckTreasuryVoucherId",
                table: "TreasuryLine");

            migrationBuilder.DeleteData(
                table: "CheckStatus",
                keyColumn: "Id",
                keyValue: new byte[] { 1, 152, 36, 25, 206, 161, 247, 233, 47, 93, 6, 121, 216, 73, 83, 36 });

            migrationBuilder.DropColumn(
                name: "BankTransferTreasuryVoucherId",
                table: "TreasuryLine");

            migrationBuilder.DropColumn(
                name: "CashTreasuryVoucherId",
                table: "TreasuryLine");

            migrationBuilder.DropColumn(
                name: "CheckTreasuryVoucherId",
                table: "TreasuryLine");

            migrationBuilder.AddColumn<byte[]>(
                name: "TreasuryVoucherId",
                table: "TreasuryLine",
                type: "varbinary(16)",
                nullable: false,
                defaultValue: new byte[0]);

            migrationBuilder.CreateTable(
                name: "Check Vault",
                columns: table => new
                {
                    Id = table.Column<byte[]>(type: "varbinary(16)", nullable: false),
                    CheckVaultName = table.Column<string>(type: "longtext", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Check Vault", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "TreasuryVoucherType",
                columns: table => new
                {
                    Id = table.Column<byte[]>(type: "varbinary(16)", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    CreatedBy = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    DeletedBy = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    IsActive = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    ModifaiedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    ModifaiedBy = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    TreasuryVoucherTypeName = table.Column<string>(type: "longtext", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TreasuryVoucherType", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "TreasuryVoucher",
                columns: table => new
                {
                    Id = table.Column<byte[]>(type: "varbinary(16)", nullable: false),
                    BankAccountId = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    BankId = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    CheckVaultId = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    CheckVaultLocationId = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    CustomerId = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    DrawerId = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    DrawerLocationId = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    EmployeeId = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    TransactionTypeId = table.Column<byte[]>(type: "varbinary(16)", nullable: false),
                    TreasuryVoucherTypeId = table.Column<byte[]>(type: "varbinary(16)", nullable: false),
                    VendorId = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    Amount = table.Column<decimal>(type: "decimal(18,5)", precision: 18, scale: 5, nullable: false),
                    BearerName = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    BeneficiaryTypeId = table.Column<byte[]>(type: "varbinary(16)", nullable: false),
                    CheckNumber = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CheckStatusId = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    CheckStatusModelId = table.Column<byte[]>(type: "varbinary(16)", nullable: true),
                    DueDate = table.Column<DateTime>(type: "Date", nullable: true),
                    IssuerName = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Note = table.Column<string>(type: "longtext", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    RefranceNumber = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    TVID = table.Column<int>(type: "int", nullable: false),
                    TransactionDate = table.Column<DateTime>(type: "Date", nullable: false),
                    VoucherSerial = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TreasuryVoucher", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TreasuryVoucher_BankAccount_BankAccountId",
                        column: x => x.BankAccountId,
                        principalTable: "BankAccount",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_TreasuryVoucher_Bank_BankId",
                        column: x => x.BankId,
                        principalTable: "Bank",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_TreasuryVoucher_Check Vault_CheckVaultId",
                        column: x => x.CheckVaultId,
                        principalTable: "Check Vault",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_TreasuryVoucher_CheckStatus_CheckStatusModelId",
                        column: x => x.CheckStatusModelId,
                        principalTable: "CheckStatus",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_TreasuryVoucher_CheckVaultLocation_CheckVaultLocationId",
                        column: x => x.CheckVaultLocationId,
                        principalTable: "CheckVaultLocation",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_TreasuryVoucher_Customer_CustomerId",
                        column: x => x.CustomerId,
                        principalTable: "Customer",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_TreasuryVoucher_DrawerLocation_DrawerLocationId",
                        column: x => x.DrawerLocationId,
                        principalTable: "DrawerLocation",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_TreasuryVoucher_Drawer_DrawerId",
                        column: x => x.DrawerId,
                        principalTable: "Drawer",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_TreasuryVoucher_Employee_EmployeeId",
                        column: x => x.EmployeeId,
                        principalTable: "Employee",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_TreasuryVoucher_TransactionType_TransactionTypeId",
                        column: x => x.TransactionTypeId,
                        principalTable: "TransactionType",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_TreasuryVoucher_TreasuryVoucherType_TreasuryVoucherTypeId",
                        column: x => x.TreasuryVoucherTypeId,
                        principalTable: "TreasuryVoucherType",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_TreasuryVoucher_Vendor_VendorId",
                        column: x => x.VendorId,
                        principalTable: "Vendor",
                        principalColumn: "Id");
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.InsertData(
                table: "TreasuryVoucherType",
                columns: new[] { "Id", "CreatedAt", "CreatedBy", "DeletedAt", "DeletedBy", "IsActive", "ModifaiedAt", "ModifaiedBy", "TreasuryVoucherTypeName" },
                values: new object[,]
                {
                    { new byte[] { 1, 150, 38, 162, 247, 55, 200, 202, 180, 202, 181, 14, 129, 53, 88, 209 }, null, null, null, null, true, null, null, "Drawer" },
                    { new byte[] { 1, 151, 251, 158, 15, 57, 235, 98, 86, 58, 195, 79, 229, 240, 45, 182 }, null, null, null, null, true, null, null, "Check" },
                    { new byte[] { 1, 150, 38, 162, 247, 55, 172, 204, 165, 197, 254, 4, 110, 60, 80, 197 }, null, null, null, null, true, null, null, "Bank" }
                });

            migrationBuilder.CreateIndex(
                name: "IX_TreasuryLine_TreasuryVoucherId",
                table: "TreasuryLine",
                column: "TreasuryVoucherId");

            migrationBuilder.CreateIndex(
                name: "IX_TreasuryVoucher_BankAccountId",
                table: "TreasuryVoucher",
                column: "BankAccountId");

            migrationBuilder.CreateIndex(
                name: "IX_TreasuryVoucher_BankId",
                table: "TreasuryVoucher",
                column: "BankId");

            migrationBuilder.CreateIndex(
                name: "IX_TreasuryVoucher_CheckStatusModelId",
                table: "TreasuryVoucher",
                column: "CheckStatusModelId");

            migrationBuilder.CreateIndex(
                name: "IX_TreasuryVoucher_CheckVaultId",
                table: "TreasuryVoucher",
                column: "CheckVaultId");

            migrationBuilder.CreateIndex(
                name: "IX_TreasuryVoucher_CheckVaultLocationId",
                table: "TreasuryVoucher",
                column: "CheckVaultLocationId");

            migrationBuilder.CreateIndex(
                name: "IX_TreasuryVoucher_CustomerId",
                table: "TreasuryVoucher",
                column: "CustomerId");

            migrationBuilder.CreateIndex(
                name: "IX_TreasuryVoucher_DrawerId",
                table: "TreasuryVoucher",
                column: "DrawerId");

            migrationBuilder.CreateIndex(
                name: "IX_TreasuryVoucher_DrawerLocationId",
                table: "TreasuryVoucher",
                column: "DrawerLocationId");

            migrationBuilder.CreateIndex(
                name: "IX_TreasuryVoucher_EmployeeId",
                table: "TreasuryVoucher",
                column: "EmployeeId");

            migrationBuilder.CreateIndex(
                name: "IX_TreasuryVoucher_TransactionTypeId",
                table: "TreasuryVoucher",
                column: "TransactionTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_TreasuryVoucher_TreasuryVoucherTypeId",
                table: "TreasuryVoucher",
                column: "TreasuryVoucherTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_TreasuryVoucher_VendorId",
                table: "TreasuryVoucher",
                column: "VendorId");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckVaultLocation_Check Vault_CheckVaultId",
                table: "CheckVaultLocation",
                column: "CheckVaultId",
                principalTable: "Check Vault",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_TreasuryLine_TreasuryVoucher_TreasuryVoucherId",
                table: "TreasuryLine",
                column: "TreasuryVoucherId",
                principalTable: "TreasuryVoucher",
                principalColumn: "Id");
        }
    }
}
