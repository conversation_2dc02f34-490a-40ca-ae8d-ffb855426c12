﻿namespace SimpleBooks.Repositories.Core.IRepository.Sales
{
    public interface ISalesOrderRepository<TEntity, TEntityView> : IBaseRepository<TEntity, TEntityView> where TEntity : BaseIdentityModel where TEntityView : BaseIdentityModel
    {
        Task<IEnumerable<OpenSalesOrderDto>> GetOpenSalesOrdersByCustomerAsync(Ulid customerId);
        Task<IEnumerable<OpenSalesOrderLineDto>> GetOpenSalesOrderLinesAsync(Ulid salesOrderId);
    }
}
