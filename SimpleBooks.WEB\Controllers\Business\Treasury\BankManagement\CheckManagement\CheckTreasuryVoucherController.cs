﻿namespace SimpleBooks.WEB.Controllers.Business.Treasury.BankManagement.CheckManagement
{
    public class CheckTreasuryVoucherController : BaseBusinessController<
        CheckTreasuryVoucherModel,
        CheckTreasuryVoucherModel,
        CreateCheckTreasuryVoucherViewModel,
        UpdateCheckTreasuryVoucherViewModel,
        IndexCheckTreasuryVoucherFormViewModel,
        CreateCheckTreasuryVoucherFormViewModel,
        UpdateCheckTreasuryVoucherFormViewModel>
    {
        private readonly ICheckTreasuryVoucherService _checkTreasuryVoucherService;

        public CheckTreasuryVoucherController(ICheckTreasuryVoucherService checkTreasuryVoucherService) : base(checkTreasuryVoucherService)
        {
            _checkTreasuryVoucherService = checkTreasuryVoucherService;
        }

        public override async Task<IActionResult> Create(CreateCheckTreasuryVoucherFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FullFillViewAsync(model);

            try
            {
                var result = await _checkTreasuryVoucherService.AddAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("checkTreasuryVoucher", ex.Message);
                return await FullFillViewAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        public override async Task<IActionResult> Update(Ulid id)
        {
            CheckTreasuryVoucherModel? entity = await _checkTreasuryVoucherService.GetByIdAsync(id);

            if (entity is null)
                return NotFound();

            UpdateCheckTreasuryVoucherFormViewModel viewModel = new UpdateCheckTreasuryVoucherFormViewModel()
            {
                Id = entity.Id,
                TransactionDate = entity.TransactionDate,
                VoucherSerial = entity.VoucherSerial,
                TVID = entity.TVID,
                Amount = entity.Amount,
                Note = entity.Note,
                RefranceNumber = entity.RefranceNumber,
                TransactionTypeId = entity.TransactionTypeId,
                CheckVaultId = entity.CheckVaultId,
                CheckVaultLocationId = entity.CheckVaultLocationId,
                BankId = entity.BankId,
                BankAccountId = entity.BankAccountId,
                CheckNumber = entity.CheckNumber,
                DueDate = entity.DueDate,
                IssuerName = entity.IssuerName,
                BearerName = entity.BearerName,
                CheckStatusId = entity.CheckStatusId,
                TreasuryLines = entity.TreasuryLines.Select(x => x.ToUpdateDto()).ToList(),
                CheckStatusHistories = entity.CheckStatusHistories.Select(x => x.ToUpdateDto()).ToList(),
                BeneficiaryTypeId = entity.BeneficiaryTypeId,
                BeneficiaryId = entity.GetBeneficiaryId(),
                BeneficiaryTypes = await _checkTreasuryVoucherService.SelectiveBeneficiaryTypeListAsync().GetDataOrThrowIfNullAsync(),
                Vendors = await _checkTreasuryVoucherService.SelectiveVendorListAsync().ToSelectListItemAsync(),
                Customers = await _checkTreasuryVoucherService.SelectiveCustomerListAsync().ToSelectListItemAsync(),
                Employees = await _checkTreasuryVoucherService.SelectiveEmployeeListAsync().ToSelectListItemAsync(),
                TransactionTypes = await _checkTreasuryVoucherService.SelectiveTransactionTypeListAsync().GetDataOrThrowIfNullAsync(),
                CheckVaults = await _checkTreasuryVoucherService.SelectiveCheckVaultListAsync().ToSelectListItemAsync(),
                CheckVaultLocations = await _checkTreasuryVoucherService.SelectiveCheckVaultLocationListAsync().GetDataOrThrowIfNullAsync(),
                Banks = await _checkTreasuryVoucherService.SelectiveBankListAsync().ToSelectListItemAsync(),
                BankAccounts = await _checkTreasuryVoucherService.SelectiveBankAccountListAsync().GetDataOrThrowIfNullAsync(),
                TreasuryLineTypes = await _checkTreasuryVoucherService.SelectiveTreasuryLineTypeListAsync().GetDataOrThrowIfNullAsync(),
                Expenses = await _checkTreasuryVoucherService.SelectiveExpenseListAsync().ToSelectListItemAsync(),
                Bills = await _checkTreasuryVoucherService.SelectiveBillListAsync().ToSelectListItemAsync(),
                BillReturns = await _checkTreasuryVoucherService.SelectiveBillReturnListAsync().ToSelectListItemAsync(),
                Invoices = await _checkTreasuryVoucherService.SelectiveInvoiceListAsync().ToSelectListItemAsync(),
                InvoiceReturns = await _checkTreasuryVoucherService.SelectiveInvoiceReturnListAsync().ToSelectListItemAsync(),
            };

            return View(viewModel);
        }

        public override async Task<IActionResult> Update(UpdateCheckTreasuryVoucherFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FailedActionResultAsync(model);

            try
            {
                var result = await _checkTreasuryVoucherService.UpdateAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("checkTreasuryVoucher", ex.Message);
                return await FailedActionResultAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        protected override async Task<IActionResult> FullFillViewAsync(CreateCheckTreasuryVoucherFormViewModel model)
        {
            model.BeneficiaryTypes = await _checkTreasuryVoucherService.SelectiveBeneficiaryTypeListAsync().GetDataOrThrowIfNullAsync();
            model.Vendors = await _checkTreasuryVoucherService.SelectiveVendorListAsync().ToSelectListItemAsync();
            model.Customers = await _checkTreasuryVoucherService.SelectiveCustomerListAsync().ToSelectListItemAsync();
            model.Employees = await _checkTreasuryVoucherService.SelectiveEmployeeListAsync().ToSelectListItemAsync();
            model.TransactionTypes = await _checkTreasuryVoucherService.SelectiveTransactionTypeListAsync().GetDataOrThrowIfNullAsync();
            model.CheckVaults = await _checkTreasuryVoucherService.SelectiveCheckVaultListAsync().ToSelectListItemAsync();
            model.CheckVaultLocations = await _checkTreasuryVoucherService.SelectiveCheckVaultLocationListAsync().GetDataOrThrowIfNullAsync();
            model.Banks = await _checkTreasuryVoucherService.SelectiveBankListAsync().ToSelectListItemAsync();
            model.BankAccounts = await _checkTreasuryVoucherService.SelectiveBankAccountListAsync().GetDataOrThrowIfNullAsync();
            model.TreasuryLineTypes = await _checkTreasuryVoucherService.SelectiveTreasuryLineTypeListAsync().GetDataOrThrowIfNullAsync();
            model.Expenses = await _checkTreasuryVoucherService.SelectiveExpenseListAsync().ToSelectListItemAsync();
            model.Bills = await _checkTreasuryVoucherService.SelectiveBillListAsync().ToSelectListItemAsync();
            model.BillReturns = await _checkTreasuryVoucherService.SelectiveBillReturnListAsync().ToSelectListItemAsync();
            model.Invoices = await _checkTreasuryVoucherService.SelectiveInvoiceListAsync().ToSelectListItemAsync();
            model.InvoiceReturns = await _checkTreasuryVoucherService.SelectiveInvoiceReturnListAsync().ToSelectListItemAsync();
            return await Task.FromResult(View(model));
        }

        protected override async Task<IActionResult> FailedActionResultAsync(UpdateCheckTreasuryVoucherFormViewModel model)
        {
            model.BeneficiaryTypes = await _checkTreasuryVoucherService.SelectiveBeneficiaryTypeListAsync().GetDataOrThrowIfNullAsync();
            model.Vendors = await _checkTreasuryVoucherService.SelectiveVendorListAsync().ToSelectListItemAsync();
            model.Customers = await _checkTreasuryVoucherService.SelectiveCustomerListAsync().ToSelectListItemAsync();
            model.Employees = await _checkTreasuryVoucherService.SelectiveEmployeeListAsync().ToSelectListItemAsync();
            model.TransactionTypes = await _checkTreasuryVoucherService.SelectiveTransactionTypeListAsync().GetDataOrThrowIfNullAsync();
            model.CheckVaults = await _checkTreasuryVoucherService.SelectiveCheckVaultListAsync().ToSelectListItemAsync();
            model.CheckVaultLocations = await _checkTreasuryVoucherService.SelectiveCheckVaultLocationListAsync().GetDataOrThrowIfNullAsync();
            model.Banks = await _checkTreasuryVoucherService.SelectiveBankListAsync().ToSelectListItemAsync();
            model.BankAccounts = await _checkTreasuryVoucherService.SelectiveBankAccountListAsync().GetDataOrThrowIfNullAsync();
            model.TreasuryLineTypes = await _checkTreasuryVoucherService.SelectiveTreasuryLineTypeListAsync().GetDataOrThrowIfNullAsync();
            model.Expenses = await _checkTreasuryVoucherService.SelectiveExpenseListAsync().ToSelectListItemAsync();
            model.Bills = await _checkTreasuryVoucherService.SelectiveBillListAsync().ToSelectListItemAsync();
            model.BillReturns = await _checkTreasuryVoucherService.SelectiveBillReturnListAsync().ToSelectListItemAsync();
            model.Invoices = await _checkTreasuryVoucherService.SelectiveInvoiceListAsync().ToSelectListItemAsync();
            model.InvoiceReturns = await _checkTreasuryVoucherService.SelectiveInvoiceReturnListAsync().ToSelectListItemAsync();
            return await Task.FromResult(View(model));
        }
    }
}
