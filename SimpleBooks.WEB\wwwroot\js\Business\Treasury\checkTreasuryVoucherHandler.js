﻿import { treasuryLineTools } from '/js/business/Treasury/treasuryLineTools.js';

export class CheckTreasuryVoucherHandler {
    constructor(treasuryLineTypes, checkVaults, checkVaultLocations, banks, bankAccounts, vendors, customers, employess, expenses, bills, billReturns, invoices, invoiceReturns) {
        this.treasuryLineTypes = treasuryLineTypes;
        this.checkVaults = checkVaults;
        this.checkVaultLocations = checkVaultLocations;
        this.banks = banks;
        this.bankAccounts = bankAccounts;
        this.vendors = vendors;
        this.customers = customers;
        this.employess = employess;
        this.expenses = expenses;
        this.bills = bills;
        this.billReturns = billReturns;
        this.invoices = invoices;
        this.invoiceReturns = invoiceReturns;
    }

    addTreasuryLineRow(includeHidden = false, treasuryVoucherId = null) {
        let hiddenValue = null;
        if (includeHidden && treasuryVoucherId) {
            hiddenValue = { TreasuryVoucherId: treasuryVoucherId };
        }
        treasuryLineTools.addRow({
            treasuryLineTypes: this.treasuryLineTypes,
            expenses: this.expenses,
            bills: this.bills,
            billReturns: this.billReturns,
            invoices: this.invoices,
            invoiceReturns: this.invoiceReturns,
            modelName: "TreasuryLines",
            tableId: "TreasuryLinesDataGridView",
            includeHidden: includeHidden && !!treasuryVoucherId,
            hiddenValue: hiddenValue
        });
    }

    onTransactionTypeChanged(transactionTypeId) {
        const bankSector = document.querySelector('.BankSector');
        const checkSector = document.querySelector('.CheckSector');

        if (transactionTypeId === '01JZBSP3PFVH37SZ8D5DEQF304') {                // TreasuryCheckIn
            bankSector.style.display = 'none';
            checkSector.style.display = 'block';
        } else if (transactionTypeId === '01JZBSP7B64B1GAKF49A3D8M9S') {         // TreasuryCheckOut
            bankSector.style.display = 'block';
            checkSector.style.display = 'none';
        }
    }

    onTreasuryVoucherTypeChanged(treasuryVoucherTypeId) {
        const bankSector = document.querySelector('.BankSector');
        const checkSector = document.querySelector('.CheckSector');

        if (treasuryVoucherTypeId === '01JZXSW3SSXDH5CEP39ZJZ0BDP') {
            bankSector.style.display = 'none';
            checkSector.style.display = 'block';
        } else if (treasuryVoucherTypeId === '01JRKA5XSQNK6ABHFY0HQ3RM65') {
            bankSector.style.display = 'block';
            checkSector.style.display = 'none';
        } else {
            bankSector.style.display = 'none';
            checkSector.style.display = 'none';
        }
    }

    onCheckVaultChanged(checkVaultId) {
        const checkVaultLocationIdSelect = document.getElementById('CheckVaultLocationId')
        if (!checkVaultLocationIdSelect) return;

        const checkVaultLocations = this.checkVaultLocations.$values.filter(x => x.CheckVaultId == checkVaultId)

        checkVaultLocationIdSelect.innerHTML = '<option value=""></option>';
        checkVaultLocations.forEach(checkVaultLocation => {
            const opt = document.createElement("option");
            opt.value = checkVaultLocation.Id;
            opt.text = checkVaultLocation.CheckVaultLocationCurrency + ' - ' + checkVaultLocation.CheckVaultLocationNumber;
            checkVaultLocationIdSelect.appendChild(opt);
        });
    }

    onBankChanged(bankId) {
        const bankAccountIdSelect = document.getElementById('BankAccountId')
        if (!bankAccountIdSelect) return;

        const bankAccounts = this.bankAccounts.$values.filter(x => x.BankId == bankId)

        bankAccountIdSelect.innerHTML = '<option value=""></option>';
        bankAccounts.forEach(bankAccount => {
            const opt = document.createElement("option");
            opt.value = bankAccount.Id;
            opt.text = bankAccount.BankAccountCurrency + ' - ' + bankAccount.BankAccountNumber;
            bankAccountIdSelect.appendChild(opt);
        });
    }

    onBeneficiaryTypeChanged(beneficiaryTypeId) {
        const updateField = (fieldId, values, triggerChange = false) => {
            const element = document.getElementById(fieldId);
            if (element && values != null) {
                element.innerHTML = '<option value=""></option>';
                values.$values.forEach(value => {
                    const opt = document.createElement("option");
                    opt.value = value.Value;
                    opt.text = value.Text;
                    element.appendChild(opt);
                });
                if (triggerChange) {
                    const event = new Event('change', { bubbles: true });
                    element.dispatchEvent(event);
                }
            }
        };

        if (beneficiaryTypeId == '01JTKF9QSYN1TR6DMBY0MZ3BY6') {
            updateField('BeneficiaryId', this.vendors, true);
        } else if (beneficiaryTypeId == '01JTKF9ZC7W32GQAFRSVP00D47') {
            updateField('BeneficiaryId', this.customers, true);
        } else if (beneficiaryTypeId == '01JTKFA29V6V59R8C3DYMX90M3') {
            updateField('BeneficiaryId', this.employess, true);
        }
    }

    onBeneficiaryChanged(beneficiaryId) {
    }

    onTreasuryLineTypeChanged(treasuryLineTypeId, index) {
        const updateField = (fieldId, values, triggerChange = false) => {
            const row = document.querySelector(`#TreasuryLinesDataGridView tr[data-row-index="${index}"]`);
            if (!row) return;
            const element = row.querySelector(`select[name$='.${fieldId}']`);
            if (element && values != null) {
                element.innerHTML = '<option value=""></option>';
                values.$values.forEach(value => {
                    const opt = document.createElement("option");
                    opt.value = value.Value;
                    opt.text = value.Text;
                    element.appendChild(opt);
                });
                if (triggerChange) {
                    const event = new Event('change', { bubbles: true });
                    element.dispatchEvent(event);
                }
            }
        };

        if (treasuryLineTypeId == '01JTKFY7VKEN2E9533K0R9D233') {
            updateField('AccountId', this.expenses, true);
        } else if (treasuryLineTypeId == '01JTKFYF9KA5J9VP3FF37C1YBD') {
            updateField('AccountId', this.bills, true);
        } else if (treasuryLineTypeId == '01JTKGCDENPTX3SJHM4F4RM5E8') {
            updateField('AccountId', this.billReturns, true);
        } else if (treasuryLineTypeId == '01JTKGCJA6QHV54HQPPN251GGW') {
            updateField('AccountId', this.invoices, true);
        } else if (treasuryLineTypeId == '01JTKGCN8AADCYH079T7GGG6PZ') {
            updateField('AccountId', this.invoiceReturns, true);
        }
    }
}
