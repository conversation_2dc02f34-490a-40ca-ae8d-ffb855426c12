﻿namespace SimpleBooks.Models.ViewModel.Sales.SalesOrder
{
    public class IndexSalesOrderViewModel : BaseIdentityModel
    {
        [DisplayName("SalesOrder Id")]
        public string SalesOrderId { get; set; }
        [DisplayName("SalesOrder Date")]
        public DateOnly SalesOrderDate { get; set; }
        [DisplayName("SalesOrder Due Date")]
        public DateOnly SalesOrderDueDate { get; set; }
        [DisplayName("Customer Name")]
        public string CustomerName { get; set; }
        [DisplayName("Customer Type Name")]
        public string CustomerTypeName { get; set; }
        [DisplayName("Customer Rep Name")]
        public string CustomerRepName { get; set; }
        [DisplayName("Payment Term Name")]
        public string PaymentTermName { get; set; }
        [DisplayName("Amount")]
        public decimal Amount { get; set; }
    }
}
