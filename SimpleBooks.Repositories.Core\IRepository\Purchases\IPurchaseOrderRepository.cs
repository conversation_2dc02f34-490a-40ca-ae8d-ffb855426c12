﻿namespace SimpleBooks.Repositories.Core.IRepository.Purchases
{
    public interface IPurchaseOrderRepository<TEntity, TEntityView> : IBaseRepository<TEntity, TEntityView> where TEntity : BaseIdentityModel where TEntityView : BaseIdentityModel
    {
        Task<IEnumerable<OpenPurchaseOrderDto>> GetOpenPurchaseOrdersByVendorAsync(Ulid vendorId);
        Task<IEnumerable<OpenPurchaseOrderLineDto>> GetOpenPurchaseOrderLinesAsync(Ulid purchaseOrderId);
    }
}
