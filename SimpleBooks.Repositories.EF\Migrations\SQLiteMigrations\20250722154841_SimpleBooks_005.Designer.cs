﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using SimpleBooks.Repositories.EF.Factory.Contexts;

#nullable disable

namespace SimpleBooks.Repositories.EF.Migrations.SQLiteMigrations
{
    [DbContext(typeof(SQLiteApplicationDBContext))]
    [Migration("20250722154841_SimpleBooks_005")]
    partial class SimpleBooks_005
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "8.0.14");

            modelBuilder.Entity("SimpleBooks.Models.Model.BeneficiaryTypeModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<string>("BeneficiaryTypeName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.<PERSON>("Id");

                    b.ToTable("BeneficiaryType");

                    b.HasData(
                        new
                        {
                            Id = new byte[] { 1, 150, 166, 244, 223, 62, 168, 117, 131, 54, 139, 240, 41, 241, 175, 198 },
                            BeneficiaryTypeName = "Vendor"
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 166, 244, 253, 135, 224, 197, 11, 169, 248, 206, 236, 0, 52, 135 },
                            BeneficiaryTypeName = "Customer"
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 166, 245, 9, 59, 54, 202, 156, 33, 131, 111, 169, 212, 130, 131 },
                            BeneficiaryTypeName = "Employee"
                        });
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.HR.EmployeeModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<string>("EmployeeEMail")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<bool>("EmployeeIsRep")
                        .HasColumnType("INTEGER");

                    b.Property<string>("EmployeeName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("EmployeePhone")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("UserId")
                        .HasColumnType("BLOB");

                    b.HasKey("Id");

                    b.HasIndex("UserId")
                        .IsUnique();

                    b.ToTable("Employee");

                    b.HasData(
                        new
                        {
                            Id = new byte[] { 1, 150, 38, 123, 154, 173, 243, 8, 167, 58, 215, 95, 43, 211, 236, 80 },
                            EmployeeEMail = "<EMAIL>",
                            EmployeeIsRep = false,
                            EmployeeName = "Admin",
                            EmployeePhone = "1234567890",
                            IsActive = true,
                            UserId = new byte[] { 1, 150, 38, 121, 203, 122, 118, 51, 61, 199, 240, 135, 72, 219, 146, 128 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 38, 123, 214, 47, 163, 76, 21, 24, 136, 82, 106, 4, 132, 66 },
                            EmployeeEMail = "<EMAIL>",
                            EmployeeIsRep = false,
                            EmployeeName = "User",
                            EmployeePhone = "1234567890",
                            IsActive = true,
                            UserId = new byte[] { 1, 150, 38, 121, 246, 220, 46, 60, 7, 219, 10, 182, 192, 103, 38, 9 }
                        });
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Purchases.BillModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<DateOnly>("BillDate")
                        .HasColumnType("Date");

                    b.Property<DateOnly>("BillDueDate")
                        .HasColumnType("Date");

                    b.Property<string>("BillId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("PaymentTermId")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("VendorId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("VendorTypeId")
                        .HasColumnType("BLOB");

                    b.HasKey("Id");

                    b.HasIndex("PaymentTermId");

                    b.HasIndex("VendorId");

                    b.HasIndex("VendorTypeId");

                    b.ToTable("Bill");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Purchases.BillReturnModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<DateOnly>("BillReturnDate")
                        .HasColumnType("Date");

                    b.Property<DateOnly>("BillReturnDueDate")
                        .HasColumnType("Date");

                    b.Property<string>("BillReturnId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("PaymentTermId")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("VendorId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("VendorTypeId")
                        .HasColumnType("BLOB");

                    b.HasKey("Id");

                    b.HasIndex("PaymentTermId");

                    b.HasIndex("VendorId");

                    b.HasIndex("VendorTypeId");

                    b.ToTable("BillReturn");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Purchases.VendorModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("PaymentTermId")
                        .HasColumnType("BLOB");

                    b.Property<string>("VendorName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("VendorTaxCardNumber")
                        .IsRequired()
                        .HasMaxLength(9)
                        .HasColumnType("TEXT");

                    b.Property<byte[]>("VendorTypeId")
                        .HasColumnType("BLOB");

                    b.HasKey("Id");

                    b.HasIndex("PaymentTermId");

                    b.HasIndex("VendorTypeId");

                    b.ToTable("Vendor");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Purchases.VendorTypeModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<string>("VendorTypeName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("VendorType");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Sales.CustomerModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<string>("CustomerName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<byte[]>("CustomerRepId")
                        .HasColumnType("BLOB");

                    b.Property<string>("CustomerTaxCardNumber")
                        .IsRequired()
                        .HasMaxLength(9)
                        .HasColumnType("TEXT");

                    b.Property<byte[]>("CustomerTypeId")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("PaymentTermId")
                        .HasColumnType("BLOB");

                    b.HasKey("Id");

                    b.HasIndex("CustomerRepId");

                    b.HasIndex("CustomerTypeId");

                    b.HasIndex("PaymentTermId");

                    b.ToTable("Customer");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Sales.CustomerTypeModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<string>("CustomerTypeName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("CustomerType");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Sales.InvoiceModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("CustomerId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("CustomerRepId")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("CustomerTypeId")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<DateOnly>("InvoiceDate")
                        .HasColumnType("Date");

                    b.Property<DateOnly>("InvoiceDueDate")
                        .HasColumnType("Date");

                    b.Property<string>("InvoiceId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("PaymentTermId")
                        .HasColumnType("BLOB");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.HasIndex("CustomerRepId");

                    b.HasIndex("CustomerTypeId");

                    b.HasIndex("PaymentTermId");

                    b.ToTable("Invoice");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Sales.InvoiceReturnModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("CustomerId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("CustomerRepId")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("CustomerTypeId")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<DateOnly>("InvoiceReturnDate")
                        .HasColumnType("Date");

                    b.Property<DateOnly>("InvoiceReturnDueDate")
                        .HasColumnType("Date");

                    b.Property<string>("InvoiceReturnId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("PaymentTermId")
                        .HasColumnType("BLOB");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.HasIndex("CustomerRepId");

                    b.HasIndex("CustomerTypeId");

                    b.HasIndex("PaymentTermId");

                    b.ToTable("InvoiceReturn");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Tax.TaxSubTypeModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Desc_ar")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Desc_en")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<byte[]>("TaxTypeId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.HasKey("Id");

                    b.HasIndex("TaxTypeId");

                    b.ToTable("TaxSubType");

                    b.HasData(
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 146, 143, 197, 130, 113, 42, 238, 190, 163, 28, 198 },
                            Code = "V001",
                            Desc_ar = "تصدير للخارج",
                            Desc_en = "Export",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 144, 143, 78, 159, 241, 17, 76, 44, 215, 60, 226 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 146, 247, 16, 194, 93, 91, 252, 75, 71, 231, 69 },
                            Code = "V002",
                            Desc_ar = "تصدير مناطق حرة وأخرى",
                            Desc_en = "Export to free areas and other areas",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 144, 143, 78, 159, 241, 17, 76, 44, 215, 60, 226 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 146, 108, 46, 114, 200, 248, 92, 235, 172, 234, 239 },
                            Code = "V003",
                            Desc_ar = "سلعة أو خدمة معفاة",
                            Desc_en = "Exempted good or service",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 144, 143, 78, 159, 241, 17, 76, 44, 215, 60, 226 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 146, 170, 75, 223, 12, 151, 125, 185, 87, 68, 25 },
                            Code = "V004",
                            Desc_ar = "سلعة أو خدمة غير خاضعة للضريبة",
                            Desc_en = "A non-taxable good or service",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 144, 143, 78, 159, 241, 17, 76, 44, 215, 60, 226 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 146, 29, 84, 16, 149, 139, 140, 123, 89, 82, 190 },
                            Code = "V005",
                            Desc_ar = "إعفاءات دبلوماسين والقنصليات والسفارات",
                            Desc_en = "Exemptions for diplomats, consulates and embassies",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 144, 143, 78, 159, 241, 17, 76, 44, 215, 60, 226 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 146, 15, 178, 205, 40, 81, 54, 141, 151, 52, 118 },
                            Code = "V006",
                            Desc_ar = "إعفاءات الدفاع والأمن القومى",
                            Desc_en = "Defence and National security Exemptions",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 144, 143, 78, 159, 241, 17, 76, 44, 215, 60, 226 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 147, 190, 62, 247, 45, 46, 214, 21, 96, 110, 255 },
                            Code = "V007",
                            Desc_ar = "إعفاءات اتفاقيات",
                            Desc_en = "Agreements exemptions",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 144, 143, 78, 159, 241, 17, 76, 44, 215, 60, 226 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 147, 219, 15, 156, 235, 222, 168, 17, 56, 225, 86 },
                            Code = "V008",
                            Desc_ar = "إعفاءات خاصة و أخرى",
                            Desc_en = "Special Exemptios and other reasons",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 144, 143, 78, 159, 241, 17, 76, 44, 215, 60, 226 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 147, 54, 203, 24, 131, 132, 181, 127, 143, 234, 123 },
                            Code = "V009",
                            Desc_ar = "سلع عامة",
                            Desc_en = "General Item sales",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 144, 143, 78, 159, 241, 17, 76, 44, 215, 60, 226 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 147, 207, 89, 17, 249, 212, 32, 43, 176, 36, 210 },
                            Code = "V010",
                            Desc_ar = "نسب ضريبة أخرى",
                            Desc_en = "Other Rates",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 144, 143, 78, 159, 241, 17, 76, 44, 215, 60, 226 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 147, 210, 146, 106, 198, 159, 84, 54, 223, 1, 198 },
                            Code = "Tbl01",
                            Desc_ar = "ضريبه الجدول (نسبيه)",
                            Desc_en = "Table tax (percentage)",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 145, 208, 150, 136, 30, 191, 223, 77, 32, 44, 252 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 147, 102, 76, 232, 149, 136, 128, 103, 161, 141, 236 },
                            Code = "Tbl02",
                            Desc_ar = "ضريبه الجدول (النوعية)",
                            Desc_en = "Table tax (Fixed Amount)",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 145, 184, 104, 231, 165, 235, 99, 116, 38, 168, 185 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 147, 9, 187, 46, 214, 236, 73, 22, 139, 6, 195 },
                            Code = "W001",
                            Desc_ar = "المقاولات",
                            Desc_en = "Contracting",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 145, 165, 113, 105, 161, 154, 162, 190, 62, 223, 83 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 147, 228, 150, 75, 119, 124, 95, 64, 17, 165, 123 },
                            Code = "W002",
                            Desc_ar = "التوريدات",
                            Desc_en = "Supplies",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 145, 165, 113, 105, 161, 154, 162, 190, 62, 223, 83 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 147, 95, 57, 49, 181, 26, 232, 209, 179, 254, 204 },
                            Code = "W003",
                            Desc_ar = "المشتريات",
                            Desc_en = "Purachases",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 145, 165, 113, 105, 161, 154, 162, 190, 62, 223, 83 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 147, 158, 185, 153, 142, 79, 218, 12, 123, 54, 24 },
                            Code = "W004",
                            Desc_ar = "الخدمات",
                            Desc_en = "Services",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 145, 165, 113, 105, 161, 154, 162, 190, 62, 223, 83 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 147, 163, 27, 182, 157, 248, 105, 151, 181, 86, 4 },
                            Code = "W005",
                            Desc_ar = "المبالغالتي تدفعها الجميعات التعاونية للنقل بالسيارات لاعضائها",
                            Desc_en = "Sumspaid by the cooperative societies for car transportation to their members",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 145, 165, 113, 105, 161, 154, 162, 190, 62, 223, 83 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 147, 218, 254, 80, 217, 163, 85, 67, 101, 7, 234 },
                            Code = "W006",
                            Desc_ar = "الوكالةبالعمولة والسمسرة",
                            Desc_en = "Commissionagency & brokerage",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 145, 165, 113, 105, 161, 154, 162, 190, 62, 223, 83 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 147, 239, 189, 123, 27, 51, 241, 191, 149, 12, 14 },
                            Code = "W007",
                            Desc_ar = "الخصوماتوالمنح والحوافز الاستثنائية ةالاضافية التي تمنحها شركات الدخان والاسمنت ",
                            Desc_en = "Discounts& grants & additional exceptional incentives granted by smoke &cement companies",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 145, 165, 113, 105, 161, 154, 162, 190, 62, 223, 83 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 147, 104, 130, 191, 79, 192, 230, 98, 205, 232, 144 },
                            Code = "W008",
                            Desc_ar = "جميعالخصومات والمنح والعمولات  التيتمنحها  شركات البترول والاتصالات ...وغيرها من الشركات المخاطبة بنظام الخصم",
                            Desc_en = "Alldiscounts & grants & commissions granted by petroleum &telecommunications & other companies",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 145, 165, 113, 105, 161, 154, 162, 190, 62, 223, 83 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 148, 153, 124, 83, 8, 149, 171, 252, 54, 80, 93 },
                            Code = "W009",
                            Desc_ar = "مساندة دعم الصادرات التي يمنحها صندوق تنمية الصادرات ",
                            Desc_en = "Supporting export subsidies",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 145, 165, 113, 105, 161, 154, 162, 190, 62, 223, 83 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 148, 210, 2, 191, 253, 251, 206, 14, 84, 123, 116 },
                            Code = "W010",
                            Desc_ar = "اتعاب مهنية",
                            Desc_en = "Professional fees",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 145, 165, 113, 105, 161, 154, 162, 190, 62, 223, 83 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 148, 189, 213, 95, 222, 48, 90, 175, 243, 147, 251 },
                            Code = "W011",
                            Desc_ar = "العمولة والسمسرة _م_57",
                            Desc_en = "Commission & brokerage _A_57",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 145, 165, 113, 105, 161, 154, 162, 190, 62, 223, 83 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 148, 45, 91, 193, 92, 75, 222, 12, 9, 108, 113 },
                            Code = "W012",
                            Desc_ar = "تحصيل المستشفيات من الاطباء",
                            Desc_en = "Hospitals collecting from doctors",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 145, 165, 113, 105, 161, 154, 162, 190, 62, 223, 83 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 148, 139, 97, 215, 24, 147, 176, 173, 19, 57, 196 },
                            Code = "W013",
                            Desc_ar = "الاتاوات",
                            Desc_en = "Royalties",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 145, 165, 113, 105, 161, 154, 162, 190, 62, 223, 83 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 148, 180, 23, 56, 10, 77, 139, 253, 204, 165, 241 },
                            Code = "W014",
                            Desc_ar = "تخليص جمركي ",
                            Desc_en = "Customs clearance",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 145, 165, 113, 105, 161, 154, 162, 190, 62, 223, 83 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 148, 105, 72, 94, 3, 108, 44, 109, 99, 50, 145 },
                            Code = "W015",
                            Desc_ar = "أعفاء",
                            Desc_en = "Exemption",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 145, 165, 113, 105, 161, 154, 162, 190, 62, 223, 83 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 148, 81, 21, 77, 194, 159, 188, 105, 48, 167, 143 },
                            Code = "W016",
                            Desc_ar = "دفعات مقدمه",
                            Desc_en = "advance payments",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 145, 165, 113, 105, 161, 154, 162, 190, 62, 223, 83 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 148, 242, 185, 140, 95, 95, 129, 203, 217, 252, 200 },
                            Code = "ST01",
                            Desc_ar = "ضريبه الدمغه (نسبيه)",
                            Desc_en = "Stamping tax (percentage)",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 145, 220, 119, 160, 75, 9, 56, 125, 187, 73, 34 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 148, 32, 212, 18, 22, 203, 199, 184, 5, 33, 145 },
                            Code = "ST02",
                            Desc_ar = "ضريبه الدمغه (قطعيه بمقدار ثابت)",
                            Desc_en = "Stamping Tax (amount)",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 146, 11, 213, 170, 170, 227, 41, 0, 188, 28, 113 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 148, 157, 157, 60, 2, 39, 20, 118, 174, 204, 120 },
                            Code = "Ent01",
                            Desc_ar = "ضريبة الملاهى (نسبة)",
                            Desc_en = "Entertainment tax (rate)",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 146, 101, 73, 35, 117, 149, 148, 22, 204, 197, 45 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 148, 55, 140, 94, 229, 157, 106, 74, 27, 240, 70 },
                            Code = "Ent02",
                            Desc_ar = "ضريبة الملاهى (قطعية)",
                            Desc_en = "Entertainment tax (amount)",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 146, 101, 73, 35, 117, 149, 148, 22, 204, 197, 45 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 148, 49, 232, 117, 213, 114, 75, 213, 108, 16, 114 },
                            Code = "RD01",
                            Desc_ar = "رسم تنميه الموارد (نسبة)",
                            Desc_en = "Resource development fee (rate)",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 146, 59, 56, 193, 59, 235, 194, 230, 209, 97, 242 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 148, 26, 150, 191, 82, 157, 76, 57, 226, 232, 192 },
                            Code = "RD02",
                            Desc_ar = "رسم تنميه الموارد (قطعية)",
                            Desc_en = "Resource development fee (amount)",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 146, 59, 56, 193, 59, 235, 194, 230, 209, 97, 242 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 148, 127, 150, 157, 89, 57, 86, 39, 17, 130, 62 },
                            Code = "SC01",
                            Desc_ar = "رسم خدمة (نسبة)",
                            Desc_en = "Service charges (rate)",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 146, 141, 96, 91, 131, 5, 85, 35, 149, 237, 5 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 148, 85, 138, 100, 44, 170, 43, 47, 115, 162, 100 },
                            Code = "SC02",
                            Desc_ar = "رسم خدمة (قطعية)",
                            Desc_en = "Service charges (amount)",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 146, 141, 96, 91, 131, 5, 85, 35, 149, 237, 5 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 149, 106, 201, 12, 18, 164, 173, 34, 16, 137, 217 },
                            Code = "Mn01",
                            Desc_ar = "رسم المحليات (نسبة)",
                            Desc_en = "Municipality Fees (rate)",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 146, 11, 187, 77, 113, 247, 186, 23, 235, 31, 25 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 108, 23, 27, 121, 252, 203, 166, 32, 66, 146, 173, 75, 209 },
                            Code = "Mn02",
                            Desc_ar = "رسم المحليات (قطعية)",
                            Desc_en = "Municipality Fees (amount)",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 146, 11, 187, 77, 113, 247, 186, 23, 235, 31, 25 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 108, 23, 27, 197, 239, 14, 199, 117, 38, 48, 170, 37, 16 },
                            Code = "MI01",
                            Desc_ar = "رسم التامين الصحى (نسبة)",
                            Desc_en = "Medical insurance fee (rate)",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 146, 25, 149, 248, 143, 89, 253, 3, 126, 246, 226 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 108, 23, 27, 87, 206, 149, 13, 33, 73, 82, 228, 159, 254 },
                            Code = "MI02",
                            Desc_ar = "رسم التامين الصحى (قطعية)",
                            Desc_en = "Medical insurance fee (amount)",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 146, 25, 149, 248, 143, 89, 253, 3, 126, 246, 226 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 108, 23, 27, 241, 160, 115, 81, 143, 112, 73, 98, 189, 61 },
                            Code = "OF01",
                            Desc_ar = "رسوم أخرى (نسبة)",
                            Desc_en = "Other fees (rate)",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 146, 39, 74, 193, 215, 208, 107, 115, 247, 65, 212 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 108, 23, 27, 197, 39, 132, 40, 131, 233, 66, 159, 92, 254 },
                            Code = "OF02",
                            Desc_ar = "رسوم أخرى (قطعية)",
                            Desc_en = "Other fees (amount)",
                            TaxTypeId = new byte[] { 1, 150, 42, 105, 186, 146, 39, 74, 193, 215, 208, 107, 115, 247, 65, 212 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 108, 23, 27, 58, 33, 35, 227, 122, 243, 195, 131, 134, 205 },
                            Code = "ST03",
                            Desc_ar = "ضريبه الدمغه (نسبيه)",
                            Desc_en = "Stamping tax (percentage)",
                            TaxTypeId = new byte[] { 1, 150, 42, 108, 23, 28, 242, 70, 87, 132, 228, 237, 232, 20, 248, 58 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 108, 23, 27, 235, 103, 78, 85, 110, 73, 34, 98, 187, 35 },
                            Code = "ST04",
                            Desc_ar = "ضريبه الدمغه (قطعيه بمقدار ثابت)",
                            Desc_en = "Stamping Tax (amount)",
                            TaxTypeId = new byte[] { 1, 150, 42, 108, 23, 28, 28, 14, 45, 226, 68, 184, 152, 161, 75, 91 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 108, 23, 28, 193, 127, 162, 215, 92, 225, 89, 86, 79, 90 },
                            Code = "Ent03",
                            Desc_ar = "ضريبة الملاهى (نسبة)",
                            Desc_en = "Entertainment tax (rate)",
                            TaxTypeId = new byte[] { 1, 150, 42, 108, 23, 28, 129, 231, 105, 82, 107, 106, 163, 253, 197, 177 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 108, 23, 28, 134, 213, 176, 10, 233, 208, 5, 2, 50, 38 },
                            Code = "Ent04",
                            Desc_ar = "ضريبة الملاهى (قطعية)",
                            Desc_en = "Entertainment tax (amount)",
                            TaxTypeId = new byte[] { 1, 150, 42, 108, 23, 28, 129, 231, 105, 82, 107, 106, 163, 253, 197, 177 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 108, 23, 28, 236, 170, 190, 174, 209, 225, 4, 233, 194, 219 },
                            Code = "RD03",
                            Desc_ar = "رسم تنميه الموارد (نسبة)",
                            Desc_en = "Resource development fee (rate)",
                            TaxTypeId = new byte[] { 1, 150, 42, 108, 23, 28, 61, 154, 88, 173, 227, 41, 135, 177, 202, 167 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 108, 23, 28, 194, 137, 113, 9, 80, 161, 25, 129, 32, 122 },
                            Code = "RD04",
                            Desc_ar = "رسم تنميه الموارد (قطعية)",
                            Desc_en = "Resource development fee (amount)",
                            TaxTypeId = new byte[] { 1, 150, 42, 108, 23, 28, 61, 154, 88, 173, 227, 41, 135, 177, 202, 167 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 108, 23, 28, 193, 42, 225, 180, 163, 112, 116, 26, 122, 217 },
                            Code = "SC03",
                            Desc_ar = "رسم خدمة (نسبة)",
                            Desc_en = "Service charges (rate)",
                            TaxTypeId = new byte[] { 1, 150, 42, 108, 23, 28, 127, 185, 76, 28, 153, 147, 58, 40, 19, 181 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 108, 23, 28, 37, 93, 105, 118, 210, 210, 52, 242, 103, 98 },
                            Code = "SC04",
                            Desc_ar = "رسم خدمة (قطعية)",
                            Desc_en = "Service charges (amount)",
                            TaxTypeId = new byte[] { 1, 150, 42, 108, 23, 28, 127, 185, 76, 28, 153, 147, 58, 40, 19, 181 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 108, 23, 28, 11, 21, 8, 107, 100, 57, 226, 242, 165, 77 },
                            Code = "Mn03",
                            Desc_ar = "رسم المحليات (نسبة)",
                            Desc_en = "Municipality Fees (rate)",
                            TaxTypeId = new byte[] { 1, 150, 42, 108, 23, 28, 90, 106, 176, 117, 249, 240, 32, 157, 192, 153 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 108, 23, 28, 34, 111, 193, 210, 180, 202, 59, 1, 105, 52 },
                            Code = "Mn04",
                            Desc_ar = "رسم المحليات (قطعية)",
                            Desc_en = "Municipality Fees (amount)",
                            TaxTypeId = new byte[] { 1, 150, 42, 108, 23, 28, 90, 106, 176, 117, 249, 240, 32, 157, 192, 153 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 108, 23, 28, 158, 108, 61, 29, 42, 135, 38, 252, 200, 142 },
                            Code = "MI03",
                            Desc_ar = "رسم التامين الصحى (نسبة)",
                            Desc_en = "Medical insurance fee (rate)",
                            TaxTypeId = new byte[] { 1, 150, 42, 108, 23, 28, 118, 147, 130, 38, 184, 61, 245, 245, 32, 1 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 108, 23, 28, 38, 156, 128, 40, 174, 11, 109, 86, 167, 46 },
                            Code = "MI04",
                            Desc_ar = "رسم التامين الصحى (قطعية)",
                            Desc_en = "Medical insurance fee (amount)",
                            TaxTypeId = new byte[] { 1, 150, 42, 108, 23, 28, 118, 147, 130, 38, 184, 61, 245, 245, 32, 1 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 108, 23, 28, 153, 72, 139, 184, 216, 155, 118, 138, 207, 191 },
                            Code = "OF03",
                            Desc_ar = "رسوم أخرى (نسبة)",
                            Desc_en = "Other fees (rate)",
                            TaxTypeId = new byte[] { 1, 150, 42, 108, 23, 28, 171, 150, 75, 83, 145, 155, 124, 137, 185, 220 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 108, 23, 28, 9, 184, 45, 50, 90, 119, 108, 177, 9, 241 },
                            Code = "OF04",
                            Desc_ar = "رسوم أخرى (قطعية)",
                            Desc_en = "Other fees (amount)",
                            TaxTypeId = new byte[] { 1, 150, 42, 108, 23, 28, 171, 150, 75, 83, 145, 155, 124, 137, 185, 220 }
                        });
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Tax.TaxTypeModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Desc_ar")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Desc_en")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsAddition")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.ToTable("TaxType");

                    b.HasData(
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 144, 143, 78, 159, 241, 17, 76, 44, 215, 60, 226 },
                            Code = "T1",
                            Desc_ar = "ضريبه القيمه المضافه",
                            Desc_en = "Value added tax",
                            IsAddition = true
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 145, 208, 150, 136, 30, 191, 223, 77, 32, 44, 252 },
                            Code = "T2",
                            Desc_ar = "ضريبه الجدول (نسبيه)",
                            Desc_en = "Table tax (percentage)",
                            IsAddition = true
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 145, 184, 104, 231, 165, 235, 99, 116, 38, 168, 185 },
                            Code = "T3",
                            Desc_ar = "ضريبه الجدول (قطعيه)",
                            Desc_en = "Table tax (Fixed Amount)",
                            IsAddition = true
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 145, 165, 113, 105, 161, 154, 162, 190, 62, 223, 83 },
                            Code = "T4",
                            Desc_ar = "الخصم تحت حساب الضريبه",
                            Desc_en = "Withholding tax (WHT)",
                            IsAddition = false
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 145, 220, 119, 160, 75, 9, 56, 125, 187, 73, 34 },
                            Code = "T5",
                            Desc_ar = "ضريبه الدمغه (نسبيه)",
                            Desc_en = "Stamping tax (percentage)",
                            IsAddition = true
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 146, 11, 213, 170, 170, 227, 41, 0, 188, 28, 113 },
                            Code = "T6",
                            Desc_ar = "ضريبه الدمغه (قطعيه بمقدار ثابت )",
                            Desc_en = "Stamping Tax (amount)",
                            IsAddition = true
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 146, 101, 73, 35, 117, 149, 148, 22, 204, 197, 45 },
                            Code = "T7",
                            Desc_ar = "ضريبة الملاهى",
                            Desc_en = "Entertainment tax",
                            IsAddition = true
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 146, 59, 56, 193, 59, 235, 194, 230, 209, 97, 242 },
                            Code = "T8",
                            Desc_ar = "رسم تنميه الموارد",
                            Desc_en = "Resource development fee",
                            IsAddition = true
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 146, 141, 96, 91, 131, 5, 85, 35, 149, 237, 5 },
                            Code = "T9",
                            Desc_ar = "رسم خدمة",
                            Desc_en = "Table tax (percentage)",
                            IsAddition = true
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 146, 11, 187, 77, 113, 247, 186, 23, 235, 31, 25 },
                            Code = "T10",
                            Desc_ar = "رسم المحليات",
                            Desc_en = "Municipality Fees",
                            IsAddition = true
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 146, 25, 149, 248, 143, 89, 253, 3, 126, 246, 226 },
                            Code = "T11",
                            Desc_ar = "رسم التامين الصحى",
                            Desc_en = "Medical insurance fee",
                            IsAddition = true
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 105, 186, 146, 39, 74, 193, 215, 208, 107, 115, 247, 65, 212 },
                            Code = "T12",
                            Desc_ar = "رسوم أخري",
                            Desc_en = "Other fees",
                            IsAddition = true
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 108, 23, 28, 242, 70, 87, 132, 228, 237, 232, 20, 248, 58 },
                            Code = "T13",
                            Desc_ar = "ضريبه الدمغه (نسبيه)",
                            Desc_en = "Stamping tax (percentage)",
                            IsAddition = true
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 108, 23, 28, 28, 14, 45, 226, 68, 184, 152, 161, 75, 91 },
                            Code = "T14",
                            Desc_ar = "ضريبه الدمغه (قطعيه بمقدار ثابت )",
                            Desc_en = "Stamping Tax (amount)",
                            IsAddition = true
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 108, 23, 28, 129, 231, 105, 82, 107, 106, 163, 253, 197, 177 },
                            Code = "T15",
                            Desc_ar = "ضريبة الملاهى",
                            Desc_en = "Entertainment tax",
                            IsAddition = true
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 108, 23, 28, 61, 154, 88, 173, 227, 41, 135, 177, 202, 167 },
                            Code = "T16",
                            Desc_ar = "رسم تنميه الموارد",
                            Desc_en = "Resource development fee",
                            IsAddition = true
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 108, 23, 28, 127, 185, 76, 28, 153, 147, 58, 40, 19, 181 },
                            Code = "T17",
                            Desc_ar = "رسم خدمة",
                            Desc_en = "Table tax (percentage)",
                            IsAddition = true
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 108, 23, 28, 90, 106, 176, 117, 249, 240, 32, 157, 192, 153 },
                            Code = "T18",
                            Desc_ar = "رسم المحليات",
                            Desc_en = "Municipality Fees",
                            IsAddition = true
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 108, 23, 28, 118, 147, 130, 38, 184, 61, 245, 245, 32, 1 },
                            Code = "T19",
                            Desc_ar = "رسم التامين الصحى",
                            Desc_en = "Medical insurance fee",
                            IsAddition = true
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 42, 108, 23, 28, 171, 150, 75, 83, 145, 155, 124, 137, 185, 220 },
                            Code = "T20",
                            Desc_ar = "رسوم أخرى",
                            Desc_en = "Other fees",
                            IsAddition = true
                        });
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.TransactionTypeModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<string>("TransactionTypeName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("TransactionType");

                    b.HasData(
                        new
                        {
                            Id = new byte[] { 1, 150, 38, 162, 247, 53, 139, 175, 201, 235, 0, 171, 37, 207, 48, 220 },
                            TransactionTypeName = "Bill"
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 38, 162, 247, 55, 33, 110, 218, 5, 135, 136, 117, 71, 0, 42 },
                            TransactionTypeName = "Bill Return"
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 38, 162, 247, 55, 203, 220, 72, 143, 3, 160, 78, 119, 54, 165 },
                            TransactionTypeName = "Invoice"
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 38, 162, 247, 55, 131, 110, 51, 75, 150, 58, 222, 167, 0, 216 },
                            TransactionTypeName = "Invoice Return"
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 38, 162, 247, 55, 73, 94, 89, 5, 162, 106, 227, 26, 12, 229 },
                            TransactionTypeName = "Inventory Adjustment"
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 38, 162, 247, 56, 62, 56, 9, 87, 86, 242, 122, 105, 89, 123 },
                            TransactionTypeName = "Inventory Transfer"
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 38, 162, 247, 56, 159, 217, 24, 13, 148, 125, 19, 251, 98, 167 },
                            TransactionTypeName = "Inventory Opening Balance"
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 38, 162, 247, 56, 188, 112, 197, 249, 42, 1, 168, 41, 7, 23 },
                            TransactionTypeName = "SalesOrder"
                        },
                        new
                        {
                            Id = new byte[] { 1, 151, 215, 154, 192, 143, 29, 142, 239, 97, 70, 208, 101, 226, 198, 204 },
                            TransactionTypeName = "Cash In"
                        },
                        new
                        {
                            Id = new byte[] { 1, 151, 215, 154, 223, 138, 142, 20, 70, 76, 218, 156, 211, 157, 248, 131 },
                            TransactionTypeName = "Cash Out"
                        },
                        new
                        {
                            Id = new byte[] { 1, 151, 215, 155, 14, 207, 220, 70, 124, 253, 13, 43, 93, 119, 140, 4 },
                            TransactionTypeName = "Check In"
                        },
                        new
                        {
                            Id = new byte[] { 1, 151, 215, 155, 29, 102, 34, 195, 5, 77, 228, 74, 134, 212, 81, 57 },
                            TransactionTypeName = "Check Out"
                        },
                        new
                        {
                            Id = new byte[] { 1, 151, 215, 155, 51, 171, 233, 171, 139, 25, 46, 23, 82, 111, 103, 133 },
                            TransactionTypeName = "Bank Transfer In"
                        },
                        new
                        {
                            Id = new byte[] { 1, 151, 215, 155, 77, 14, 130, 62, 149, 111, 73, 152, 167, 113, 247, 172 },
                            TransactionTypeName = "Bank Transfer Out"
                        },
                        new
                        {
                            Id = new byte[] { 1, 151, 215, 154, 237, 248, 159, 27, 234, 149, 27, 98, 60, 86, 91, 180 },
                            TransactionTypeName = "Bank In"
                        },
                        new
                        {
                            Id = new byte[] { 1, 151, 215, 155, 3, 39, 88, 24, 93, 15, 221, 228, 133, 204, 194, 214 },
                            TransactionTypeName = "Bank Out"
                        });
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.BankAccountModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<string>("BankAccountCurrency")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("BankAccountIBAN")
                        .HasColumnType("TEXT");

                    b.Property<string>("BankAccountNumber")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("BankAccountSwiftCode")
                        .HasColumnType("TEXT");

                    b.Property<byte[]>("BankId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.HasKey("Id");

                    b.HasIndex("BankId");

                    b.ToTable("BankAccount");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.BankModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<string>("BankIdentifier")
                        .HasColumnType("TEXT");

                    b.Property<string>("BankName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("Bank");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.BankTransferManagement.BankTransferTreasuryVoucherModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<decimal>("Amount")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.Property<byte[]>("BankAccountId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("BankId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("BeneficiaryTypeId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("CustomerId")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("EmployeeId")
                        .HasColumnType("BLOB");

                    b.Property<string>("Note")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("RefranceNumber")
                        .HasColumnType("TEXT");

                    b.Property<int>("TVID")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("TransactionDate")
                        .HasColumnType("Date");

                    b.Property<byte[]>("TransactionTypeId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("VendorId")
                        .HasColumnType("BLOB");

                    b.Property<int>("VoucherSerial")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("BankAccountId");

                    b.HasIndex("BankId");

                    b.HasIndex("BeneficiaryTypeId");

                    b.HasIndex("CustomerId");

                    b.HasIndex("EmployeeId");

                    b.HasIndex("TransactionTypeId");

                    b.HasIndex("VendorId");

                    b.ToTable("BankTransferTreasuryVoucher");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckClearModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<DateTime>("ClearDate")
                        .HasColumnType("Date");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.HasKey("Id");

                    b.ToTable("CheckClear");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckCollectionModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<DateTime>("CollectionDate")
                        .HasColumnType("Date");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.HasKey("Id");

                    b.ToTable("CheckCollection");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckDepositModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("BankAccountId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("BankId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<DateTime>("DepositDate")
                        .HasColumnType("Date");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.Property<string>("RefranceNumber")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("BankAccountId");

                    b.HasIndex("BankId");

                    b.ToTable("CheckDeposit");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckRejectModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.Property<string>("RefranceNumber")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("RejectDate")
                        .HasColumnType("Date");

                    b.Property<string>("RejectReason")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("CheckReject");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckReturnModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("CheckVaultId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("CheckVaultLocationId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.Property<string>("RefranceNumber")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("ReturnDate")
                        .HasColumnType("Date");

                    b.HasKey("Id");

                    b.HasIndex("CheckVaultId");

                    b.HasIndex("CheckVaultLocationId");

                    b.ToTable("CheckReturn");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckStatusHistoryModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("CheckClearId")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("CheckCollectionId")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("CheckDepositId")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("CheckRejectId")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("CheckReturnId")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("CheckStatusFromId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("CheckStatusToId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("CheckTreasuryVoucherId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.Property<string>("Note")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("TransactionDate")
                        .HasColumnType("Date");

                    b.HasKey("Id");

                    b.HasIndex("CheckClearId");

                    b.HasIndex("CheckCollectionId");

                    b.HasIndex("CheckDepositId");

                    b.HasIndex("CheckRejectId");

                    b.HasIndex("CheckReturnId");

                    b.HasIndex("CheckStatusFromId");

                    b.HasIndex("CheckStatusToId");

                    b.HasIndex("CheckTreasuryVoucherId");

                    b.ToTable("CheckStatusHistory");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckStatusModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<string>("CheckStatusName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.HasKey("Id");

                    b.ToTable("CheckStatus");

                    b.HasData(
                        new
                        {
                            Id = new byte[] { 1, 151, 251, 83, 185, 115, 212, 11, 238, 136, 104, 149, 116, 145, 77, 237 },
                            CheckStatusName = "Unknown",
                            IsActive = true
                        },
                        new
                        {
                            Id = new byte[] { 1, 151, 215, 137, 114, 56, 173, 245, 28, 6, 146, 50, 70, 177, 103, 95 },
                            CheckStatusName = "Issued",
                            IsActive = true
                        },
                        new
                        {
                            Id = new byte[] { 1, 151, 215, 137, 154, 196, 133, 117, 210, 1, 39, 242, 167, 161, 255, 31 },
                            CheckStatusName = "Cleared",
                            IsActive = true
                        },
                        new
                        {
                            Id = new byte[] { 1, 151, 215, 137, 132, 137, 157, 248, 208, 106, 73, 56, 89, 114, 241, 129 },
                            CheckStatusName = "Received",
                            IsActive = true
                        },
                        new
                        {
                            Id = new byte[] { 1, 151, 215, 137, 143, 240, 189, 180, 176, 100, 248, 173, 81, 118, 33, 202 },
                            CheckStatusName = "Deposited",
                            IsActive = true
                        },
                        new
                        {
                            Id = new byte[] { 1, 152, 36, 25, 206, 161, 247, 233, 47, 93, 6, 121, 216, 73, 83, 36 },
                            CheckStatusName = "Collected",
                            IsActive = true
                        },
                        new
                        {
                            Id = new byte[] { 1, 151, 215, 137, 180, 40, 61, 241, 132, 95, 43, 62, 121, 43, 147, 84 },
                            CheckStatusName = "Rejected",
                            IsActive = true
                        },
                        new
                        {
                            Id = new byte[] { 1, 151, 215, 137, 168, 229, 103, 255, 194, 216, 138, 199, 8, 122, 248, 150 },
                            CheckStatusName = "Returned",
                            IsActive = true
                        });
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckTreasuryVoucherModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<decimal>("Amount")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.Property<byte[]>("BankAccountId")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("BankId")
                        .HasColumnType("BLOB");

                    b.Property<string>("BearerName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<byte[]>("BeneficiaryTypeId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("CheckClearId")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("CheckCollectionId")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("CheckDepositId")
                        .HasColumnType("BLOB");

                    b.Property<string>("CheckNumber")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<byte[]>("CheckRejectId")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("CheckReturnId")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("CheckStatusId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("CheckVaultId")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("CheckVaultLocationId")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("CustomerId")
                        .HasColumnType("BLOB");

                    b.Property<DateTime>("DueDate")
                        .HasColumnType("Date");

                    b.Property<byte[]>("EmployeeId")
                        .HasColumnType("BLOB");

                    b.Property<string>("IssuerName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Note")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("RefranceNumber")
                        .HasColumnType("TEXT");

                    b.Property<int>("TVID")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("TransactionDate")
                        .HasColumnType("Date");

                    b.Property<byte[]>("TransactionTypeId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("VendorId")
                        .HasColumnType("BLOB");

                    b.Property<int>("VoucherSerial")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("BankAccountId");

                    b.HasIndex("BankId");

                    b.HasIndex("BeneficiaryTypeId");

                    b.HasIndex("CheckClearId");

                    b.HasIndex("CheckCollectionId");

                    b.HasIndex("CheckDepositId");

                    b.HasIndex("CheckRejectId");

                    b.HasIndex("CheckReturnId");

                    b.HasIndex("CheckStatusId");

                    b.HasIndex("CheckVaultId");

                    b.HasIndex("CheckVaultLocationId");

                    b.HasIndex("CustomerId");

                    b.HasIndex("EmployeeId");

                    b.HasIndex("TransactionTypeId");

                    b.HasIndex("VendorId");

                    b.ToTable("CheckTreasuryVoucher");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckVaultLocationModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("CheckVaultId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<string>("CheckVaultLocationCurrency")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("CheckVaultLocationNumber")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CheckVaultId");

                    b.ToTable("CheckVaultLocation");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckVaultModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<string>("CheckVaultName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("CheckVault");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.CashManagement.CashTreasuryVoucherModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<decimal>("Amount")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.Property<byte[]>("BeneficiaryTypeId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("CustomerId")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("DrawerId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("DrawerLocationId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("EmployeeId")
                        .HasColumnType("BLOB");

                    b.Property<string>("Note")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("RefranceNumber")
                        .HasColumnType("TEXT");

                    b.Property<int>("TVID")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("TransactionDate")
                        .HasColumnType("Date");

                    b.Property<byte[]>("TransactionTypeId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("VendorId")
                        .HasColumnType("BLOB");

                    b.Property<int>("VoucherSerial")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("BeneficiaryTypeId");

                    b.HasIndex("CustomerId");

                    b.HasIndex("DrawerId");

                    b.HasIndex("DrawerLocationId");

                    b.HasIndex("EmployeeId");

                    b.HasIndex("TransactionTypeId");

                    b.HasIndex("VendorId");

                    b.ToTable("CashTreasuryVoucher");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.CashManagement.DrawerLocationModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("DrawerId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<string>("DrawerLocationCurrency")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("DrawerLocationNumber")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("DrawerId");

                    b.ToTable("DrawerLocation");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.CashManagement.DrawerModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<string>("DrawerName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("Drawer");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.ExpensesModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<string>("ExpensesDescription")
                        .HasColumnType("TEXT");

                    b.Property<string>("ExpensesName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.HasKey("Id");

                    b.ToTable("Expenses");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.PaymentTermModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<int>("DiscountPercentage")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Discriminator")
                        .IsRequired()
                        .HasMaxLength(34)
                        .HasColumnType("TEXT");

                    b.Property<string>("PaymentTermName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("PaymentTerm");

                    b.HasDiscriminator().HasValue("PaymentTermModel");

                    b.UseTphMappingStrategy();
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.TreasuryLineModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<decimal>("Amount")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.Property<byte[]>("BankTransferTreasuryVoucherId")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("BillId")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("BillReturnId")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("CashTreasuryVoucherId")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("CheckTreasuryVoucherId")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("ExpensesId")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("InvoiceId")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("InvoiceReturnId")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("TreasuryLineTypeId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.HasKey("Id");

                    b.HasIndex("BankTransferTreasuryVoucherId");

                    b.HasIndex("BillId");

                    b.HasIndex("BillReturnId");

                    b.HasIndex("CashTreasuryVoucherId");

                    b.HasIndex("CheckTreasuryVoucherId");

                    b.HasIndex("ExpensesId");

                    b.HasIndex("InvoiceId");

                    b.HasIndex("InvoiceReturnId");

                    b.ToTable("TreasuryLine");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.User.AuditModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<string>("AffectedColumns")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("DateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("NewValues")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("OldValues")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("PrimaryKey")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("TableName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("Audit");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.User.RefreshTokenModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("ExpiresOn")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("RevokedOn")
                        .HasColumnType("TEXT");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<byte[]>("UserId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("RefreshTokens");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.User.ScreensAccessProfileDetailsModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<bool>("CanAdd")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("CanDelete")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("CanEdit")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("CanOpen")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("CanPrint")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("CanShow")
                        .HasColumnType("INTEGER");

                    b.Property<byte[]>("ScreenId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("ScreensAccessProfileId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.HasKey("Id");

                    b.HasIndex("ScreensAccessProfileId");

                    b.ToTable("ScreensAccessProfileDetails");

                    b.HasData(
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 69, 87, 65 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 215, 113, 91, 23, 139, 210, 90, 62, 161, 207, 211, 212, 19 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 70, 48, 54 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 215, 128, 206, 111, 152, 245, 207, 203, 185, 163, 37, 37, 96 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 70, 51, 70 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 215, 141, 251, 218, 221, 101, 124, 150, 52, 184, 149, 31, 122 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 70, 55, 48 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 215, 156, 1, 249, 101, 170, 105, 0, 49, 130, 133, 141, 224 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 70, 65, 87 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 215, 171, 148, 227, 201, 202, 4, 52, 219, 242, 76, 56, 167 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 70, 69, 48 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 215, 184, 21, 230, 189, 241, 16, 10, 102, 233, 215, 81, 175 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 70, 72, 69 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 215, 197, 203, 229, 12, 198, 162, 249, 155, 126, 253, 76, 92 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 70, 78, 90 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 215, 215, 229, 114, 67, 250, 106, 105, 30, 118, 191, 158, 196 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 70, 82, 89 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 215, 227, 200, 190, 9, 108, 116, 65, 203, 19, 33, 15, 144 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 70, 87, 55 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 215, 240, 228, 236, 178, 118, 9, 166, 1, 117, 86, 52, 70 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 69, 82, 90, 54, 87 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 221, 143, 155, 151, 174, 201, 71, 197, 199, 34, 127, 130, 213, 20 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 69, 82, 90, 66, 71 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 221, 143, 174, 28, 81, 29, 12, 96, 160, 164, 74, 111, 236, 232 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 70, 90, 49 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 215, 252, 62, 89, 175, 141, 219, 54, 183, 167, 93, 97, 243 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 71, 52, 54 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 216, 16, 212, 246, 29, 246, 9, 248, 50, 11, 157, 200, 23 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 71, 55, 50 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 216, 28, 65, 137, 58, 51, 220, 12, 156, 155, 205, 249, 180 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 71, 57, 74 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 216, 38, 94, 105, 38, 216, 183, 11, 177, 132, 35, 76, 102 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 71, 68, 75 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 216, 54, 113, 69, 64, 222, 18, 219, 159, 8, 98, 1, 96 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 71, 71, 81 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 216, 66, 231, 214, 133, 78, 75, 118, 239, 189, 84, 107, 76 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 71, 75, 82 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 216, 79, 28, 143, 88, 190, 61, 68, 108, 17, 81, 108, 104 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 72, 54, 56 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 216, 153, 5, 82, 189, 28, 115, 169, 39, 235, 141, 38, 235 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 72, 65, 87 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 216, 171, 147, 25, 95, 76, 149, 24, 162, 55, 190, 115, 191 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 72, 68, 67 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 216, 181, 143, 245, 31, 25, 38, 105, 242, 182, 103, 160, 63 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 72, 72, 88 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 216, 199, 179, 25, 221, 252, 247, 93, 218, 29, 212, 45, 229 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 72, 77, 71 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 216, 210, 19, 8, 221, 6, 29, 248, 241, 174, 65, 105, 12 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 72, 81, 51 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 216, 220, 109, 25, 93, 57, 241, 79, 127, 208, 95, 164, 247 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 72, 84, 54 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 216, 232, 194, 3, 188, 40, 41, 38, 77, 10, 43, 43, 175 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 72, 88, 81 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 216, 246, 234, 154, 76, 3, 20, 174, 106, 140, 228, 86, 114 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 74, 49, 50 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 217, 4, 93, 0, 221, 108, 243, 248, 38, 229, 6, 139, 170 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 74, 52, 82 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 217, 19, 23, 23, 21, 156, 127, 142, 224, 59, 54, 193, 31 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 74, 56, 89 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 217, 35, 212, 255, 43, 144, 147, 221, 122, 140, 253, 106, 140 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 74, 66, 83 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 217, 47, 62, 22, 123, 215, 67, 81, 202, 247, 93, 43, 244 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 49, 48, 49, 74, 86, 68, 68, 74, 70, 49 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 217, 60, 50, 204, 74, 208, 189, 103, 148, 108, 175, 12, 226 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 69, 87, 65 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 215, 113, 91, 23, 139, 210, 90, 62, 161, 207, 211, 212, 19 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 70, 48, 54 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 215, 128, 206, 111, 152, 245, 207, 203, 185, 163, 37, 37, 96 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 70, 51, 70 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 215, 141, 251, 218, 221, 101, 124, 150, 52, 184, 149, 31, 122 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 70, 55, 48 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 215, 156, 1, 249, 101, 170, 105, 0, 49, 130, 133, 141, 224 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 70, 65, 87 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 215, 171, 148, 227, 201, 202, 4, 52, 219, 242, 76, 56, 167 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 70, 69, 48 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 215, 184, 21, 230, 189, 241, 16, 10, 102, 233, 215, 81, 175 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 70, 72, 69 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 215, 197, 203, 229, 12, 198, 162, 249, 155, 126, 253, 76, 92 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 70, 78, 90 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 215, 215, 229, 114, 67, 250, 106, 105, 30, 118, 191, 158, 196 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 70, 82, 89 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 215, 227, 200, 190, 9, 108, 116, 65, 203, 19, 33, 15, 144 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 70, 87, 55 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 215, 240, 228, 236, 178, 118, 9, 166, 1, 117, 86, 52, 70 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 69, 82, 90, 54, 87 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 221, 143, 155, 151, 174, 201, 71, 197, 199, 34, 127, 130, 213, 20 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 69, 82, 90, 66, 71 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 221, 143, 174, 28, 81, 29, 12, 96, 160, 164, 74, 111, 236, 232 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 70, 90, 49 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 215, 252, 62, 89, 175, 141, 219, 54, 183, 167, 93, 97, 243 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 71, 52, 54 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 216, 16, 212, 246, 29, 246, 9, 248, 50, 11, 157, 200, 23 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 71, 55, 50 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 216, 28, 65, 137, 58, 51, 220, 12, 156, 155, 205, 249, 180 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 71, 57, 74 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 216, 38, 94, 105, 38, 216, 183, 11, 177, 132, 35, 76, 102 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 71, 68, 75 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 216, 54, 113, 69, 64, 222, 18, 219, 159, 8, 98, 1, 96 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 71, 71, 81 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 216, 66, 231, 214, 133, 78, 75, 118, 239, 189, 84, 107, 76 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 71, 75, 82 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 216, 79, 28, 143, 88, 190, 61, 68, 108, 17, 81, 108, 104 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 72, 54, 56 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 216, 153, 5, 82, 189, 28, 115, 169, 39, 235, 141, 38, 235 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 72, 65, 87 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 216, 171, 147, 25, 95, 76, 149, 24, 162, 55, 190, 115, 191 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 72, 68, 67 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 216, 181, 143, 245, 31, 25, 38, 105, 242, 182, 103, 160, 63 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 72, 72, 88 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 216, 199, 179, 25, 221, 252, 247, 93, 218, 29, 212, 45, 229 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 72, 77, 71 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 216, 210, 19, 8, 221, 6, 29, 248, 241, 174, 65, 105, 12 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 72, 81, 51 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 216, 220, 109, 25, 93, 57, 241, 79, 127, 208, 95, 164, 247 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 72, 84, 54 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 216, 232, 194, 3, 188, 40, 41, 38, 77, 10, 43, 43, 175 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 72, 88, 81 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 216, 246, 234, 154, 76, 3, 20, 174, 106, 140, 228, 86, 114 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 74, 49, 50 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 217, 4, 93, 0, 221, 108, 243, 248, 38, 229, 6, 139, 170 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 74, 52, 82 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 217, 19, 23, 23, 21, 156, 127, 142, 224, 59, 54, 193, 31 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 74, 56, 89 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 217, 35, 212, 255, 43, 144, 147, 221, 122, 140, 253, 106, 140 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 74, 66, 83 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 217, 47, 62, 22, 123, 215, 67, 81, 202, 247, 93, 43, 244 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        },
                        new
                        {
                            Id = new byte[] { 199, 124, 237, 211, 40, 0, 50, 48, 49, 74, 86, 68, 68, 74, 70, 49 },
                            CanAdd = false,
                            CanDelete = false,
                            CanEdit = false,
                            CanOpen = false,
                            CanPrint = false,
                            CanShow = true,
                            ScreenId = new byte[] { 1, 150, 218, 217, 60, 50, 204, 74, 208, 189, 103, 148, 108, 175, 12, 226 },
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 }
                        });
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.User.ScreensAccessProfileModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.Property<string>("ScreensAccessProfileName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<byte[]>("UserModelId")
                        .HasColumnType("BLOB");

                    b.HasKey("Id");

                    b.HasIndex("UserModelId");

                    b.ToTable("ScreensAccessProfile");

                    b.HasData(
                        new
                        {
                            Id = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 },
                            IsActive = true,
                            ScreensAccessProfileName = "Admin Profile"
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 },
                            IsActive = true,
                            ScreensAccessProfileName = "User Profile"
                        });
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.User.SettingModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("UserId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.HasKey("Id");

                    b.HasIndex("UserId")
                        .IsUnique();

                    b.ToTable("Setting");

                    b.HasData(
                        new
                        {
                            Id = new byte[] { 1, 149, 16, 148, 117, 98, 196, 76, 123, 89, 234, 170, 239, 176, 79, 16 },
                            IsActive = true,
                            UserId = new byte[] { 1, 150, 38, 121, 203, 122, 118, 51, 61, 199, 240, 135, 72, 219, 146, 128 }
                        });
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.User.UserModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("EmployeeId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<byte[]>("ScreensAccessProfileId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("SettingId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<byte[]>("UserTypeId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.HasKey("Id");

                    b.HasIndex("ScreensAccessProfileId");

                    b.ToTable("User");

                    b.HasData(
                        new
                        {
                            Id = new byte[] { 1, 150, 38, 121, 203, 122, 118, 51, 61, 199, 240, 135, 72, 219, 146, 128 },
                            EmployeeId = new byte[] { 1, 150, 38, 123, 154, 173, 243, 8, 167, 58, 215, 95, 43, 211, 236, 80 },
                            IsActive = true,
                            Password = "123456",
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 180, 89, 209, 142, 246, 149, 175, 232, 166, 230, 114, 120 },
                            SettingId = new byte[] { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 },
                            UserName = "Admin",
                            UserTypeId = new byte[] { 1, 149, 151, 18, 237, 151, 125, 98, 231, 63, 22, 85, 66, 112, 24, 40 }
                        },
                        new
                        {
                            Id = new byte[] { 1, 150, 38, 121, 246, 220, 46, 60, 7, 219, 10, 182, 192, 103, 38, 9 },
                            EmployeeId = new byte[] { 1, 150, 38, 123, 214, 47, 163, 76, 21, 24, 136, 82, 106, 4, 132, 66 },
                            IsActive = true,
                            Password = "123456",
                            ScreensAccessProfileId = new byte[] { 1, 150, 218, 207, 198, 4, 127, 249, 177, 117, 255, 35, 157, 233, 84, 127 },
                            SettingId = new byte[] { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 },
                            UserName = "User",
                            UserTypeId = new byte[] { 1, 149, 151, 19, 36, 141, 8, 106, 78, 226, 193, 68, 147, 30, 95, 157 }
                        });
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.InventoryModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("BillId")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("BillReturnId")
                        .HasColumnType("BLOB");

                    b.Property<decimal>("CostAmount")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("CostPrice")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.Property<byte[]>("InvoiceId")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("InvoiceReturnId")
                        .HasColumnType("BLOB");

                    b.Property<decimal>("NetAmount")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.Property<byte[]>("ProductId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("ProductUnitId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<decimal>("Quantity")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("SalesAmount")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("SalesPrice")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.Property<byte[]>("StoreId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<decimal>("TaxAmount")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.Property<byte[]>("TransactionTypeId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<decimal>("UnitQtyRatio")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("BillId");

                    b.HasIndex("BillReturnId");

                    b.HasIndex("InvoiceId");

                    b.HasIndex("InvoiceReturnId");

                    b.HasIndex("ProductId");

                    b.HasIndex("ProductUnitId");

                    b.HasIndex("StoreId");

                    b.HasIndex("TransactionTypeId");

                    b.ToTable("Inventory");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.InventoryTaxModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("InventoryId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<decimal>("InventoryTaxsAmount")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("InventoryTaxsRatio")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.Property<byte[]>("TaxSubTypeId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("TaxTypeId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.HasKey("Id");

                    b.HasIndex("InventoryId");

                    b.HasIndex("TaxSubTypeId");

                    b.HasIndex("TaxTypeId");

                    b.ToTable("InventoryProductTax");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.ProductCategoryModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<string>("ProductCategoryName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("ProductCategory");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.ProductModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("CreatedBy")
                        .HasColumnType("BLOB");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("DeletedBy")
                        .HasColumnType("BLOB");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifaiedAt")
                        .HasColumnType("datetime");

                    b.Property<byte[]>("ModifaiedBy")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("ProductCategoryId")
                        .HasColumnType("BLOB");

                    b.Property<string>("ProductId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("ProductName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("ProductPurchasesDescription")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("ProductSalesDescription")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<byte[]>("ProductTypeId")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("UnitModelId")
                        .HasColumnType("BLOB");

                    b.HasKey("Id");

                    b.HasIndex("ProductCategoryId");

                    b.HasIndex("ProductTypeId");

                    b.HasIndex("UnitModelId");

                    b.ToTable("Product");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.ProductTaxModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("ProductId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<decimal>("ProductTaxsRatio")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.Property<byte[]>("TaxSubTypeId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("TaxTypeId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("TaxSubTypeId");

                    b.HasIndex("TaxTypeId");

                    b.ToTable("ProductTax");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.ProductTypeModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<string>("ProductTypeName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("ProductType");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.ProductUnitModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("ProductId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<byte[]>("ProductUnitId")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<decimal>("ProductUnitRatio")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("ProductUnitSalesPrice")
                        .HasPrecision(18, 5)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("ProductUnitId");

                    b.ToTable("ProductUnit");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.StoreModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<string>("StoreName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("Store");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.UnitModel", b =>
                {
                    b.Property<byte[]>("Id")
                        .HasColumnType("BLOB");

                    b.Property<string>("UnitName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("Unit");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.PaymentTermDateDrivenModel", b =>
                {
                    b.HasBaseType("SimpleBooks.Models.Model.Treasury.PaymentTermModel");

                    b.Property<int>("DueNextMonthWithinDays")
                        .HasColumnType("INTEGER");

                    b.Property<int>("IfPaidBeforeDayOfMonth")
                        .HasColumnType("INTEGER");

                    b.Property<int>("NetDueBeforeDayOfMonth")
                        .HasColumnType("INTEGER");

                    b.ToTable("PaymentTerm");

                    b.HasDiscriminator().HasValue("PaymentTermDateDrivenModel");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.PaymentTermStandardModel", b =>
                {
                    b.HasBaseType("SimpleBooks.Models.Model.Treasury.PaymentTermModel");

                    b.Property<int>("IfPaidWithinDays")
                        .HasColumnType("INTEGER");

                    b.Property<int>("NetDueDays")
                        .HasColumnType("INTEGER");

                    b.ToTable("PaymentTerm");

                    b.HasDiscriminator().HasValue("PaymentTermStandardModel");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.HR.EmployeeModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.User.UserModel", "User")
                        .WithOne("Employee")
                        .HasForeignKey("SimpleBooks.Models.Model.HR.EmployeeModel", "UserId");

                    b.Navigation("User");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Purchases.BillModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Treasury.PaymentTermModel", "PaymentTerm")
                        .WithMany("Bills")
                        .HasForeignKey("PaymentTermId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Purchases.VendorModel", "Vendor")
                        .WithMany("Bills")
                        .HasForeignKey("VendorId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Purchases.VendorTypeModel", "VendorType")
                        .WithMany("Bills")
                        .HasForeignKey("VendorTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.Navigation("PaymentTerm");

                    b.Navigation("Vendor");

                    b.Navigation("VendorType");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Purchases.BillReturnModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Treasury.PaymentTermModel", "PaymentTerm")
                        .WithMany("BillReturns")
                        .HasForeignKey("PaymentTermId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Purchases.VendorModel", "Vendor")
                        .WithMany("BillReturns")
                        .HasForeignKey("VendorId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Purchases.VendorTypeModel", "VendorType")
                        .WithMany("BillReturns")
                        .HasForeignKey("VendorTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.Navigation("PaymentTerm");

                    b.Navigation("Vendor");

                    b.Navigation("VendorType");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Purchases.VendorModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Treasury.PaymentTermModel", "PaymentTerm")
                        .WithMany("Vendors")
                        .HasForeignKey("PaymentTermId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Purchases.VendorTypeModel", "VendorType")
                        .WithMany("Vendors")
                        .HasForeignKey("VendorTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.Navigation("PaymentTerm");

                    b.Navigation("VendorType");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Sales.CustomerModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.HR.EmployeeModel", "CustomerRep")
                        .WithMany("Customers")
                        .HasForeignKey("CustomerRepId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Sales.CustomerTypeModel", "CustomerType")
                        .WithMany("Customers")
                        .HasForeignKey("CustomerTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Treasury.PaymentTermModel", "PaymentTerm")
                        .WithMany("Customers")
                        .HasForeignKey("PaymentTermId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.Navigation("CustomerRep");

                    b.Navigation("CustomerType");

                    b.Navigation("PaymentTerm");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Sales.InvoiceModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Sales.CustomerModel", "Customer")
                        .WithMany("Invoices")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.HR.EmployeeModel", "CustomerRep")
                        .WithMany("Invoices")
                        .HasForeignKey("CustomerRepId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Sales.CustomerTypeModel", "CustomerType")
                        .WithMany("Invoices")
                        .HasForeignKey("CustomerTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Treasury.PaymentTermModel", "PaymentTerm")
                        .WithMany("Invoices")
                        .HasForeignKey("PaymentTermId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.Navigation("Customer");

                    b.Navigation("CustomerRep");

                    b.Navigation("CustomerType");

                    b.Navigation("PaymentTerm");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Sales.InvoiceReturnModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Sales.CustomerModel", "Customer")
                        .WithMany("InvoiceReturns")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.HR.EmployeeModel", "CustomerRep")
                        .WithMany("InvoiceReturns")
                        .HasForeignKey("CustomerRepId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Sales.CustomerTypeModel", "CustomerType")
                        .WithMany("InvoiceReturns")
                        .HasForeignKey("CustomerTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Treasury.PaymentTermModel", "PaymentTerm")
                        .WithMany("InvoiceReturns")
                        .HasForeignKey("PaymentTermId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.Navigation("Customer");

                    b.Navigation("CustomerRep");

                    b.Navigation("CustomerType");

                    b.Navigation("PaymentTerm");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Tax.TaxSubTypeModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Tax.TaxTypeModel", "TaxType")
                        .WithMany("TaxSubTypes")
                        .HasForeignKey("TaxTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("TaxType");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.BankAccountModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.BankModel", "Bank")
                        .WithMany("BankAccounts")
                        .HasForeignKey("BankId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("Bank");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.BankTransferManagement.BankTransferTreasuryVoucherModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.BankAccountModel", "BankAccount")
                        .WithMany("BankTransferTreasuryVouchers")
                        .HasForeignKey("BankAccountId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.BankModel", "Bank")
                        .WithMany("BankTransferTreasuryVouchers")
                        .HasForeignKey("BankId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.BeneficiaryTypeModel", "BeneficiaryType")
                        .WithMany("BankTransferTreasuryVouchers")
                        .HasForeignKey("BeneficiaryTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Sales.CustomerModel", "Customer")
                        .WithMany("BankTransferTreasuryVouchers")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.HR.EmployeeModel", "Employee")
                        .WithMany("BankTransferTreasuryVouchers")
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.TransactionTypeModel", "TransactionType")
                        .WithMany("BankTransferTreasuryVouchers")
                        .HasForeignKey("TransactionTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Purchases.VendorModel", "Vendor")
                        .WithMany("BankTransferTreasuryVouchers")
                        .HasForeignKey("VendorId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.Navigation("Bank");

                    b.Navigation("BankAccount");

                    b.Navigation("BeneficiaryType");

                    b.Navigation("Customer");

                    b.Navigation("Employee");

                    b.Navigation("TransactionType");

                    b.Navigation("Vendor");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckDepositModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.BankAccountModel", "BankAccount")
                        .WithMany("CheckDeposits")
                        .HasForeignKey("BankAccountId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.BankModel", "Bank")
                        .WithMany("CheckDeposits")
                        .HasForeignKey("BankId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("Bank");

                    b.Navigation("BankAccount");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckReturnModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckVaultModel", "CheckVault")
                        .WithMany("CheckReturns")
                        .HasForeignKey("CheckVaultId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckVaultLocationModel", "CheckVaultLocation")
                        .WithMany("CheckReturns")
                        .HasForeignKey("CheckVaultLocationId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("CheckVault");

                    b.Navigation("CheckVaultLocation");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckStatusHistoryModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckClearModel", "CheckClear")
                        .WithMany("CheckStatusHistories")
                        .HasForeignKey("CheckClearId");

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckCollectionModel", "CheckCollection")
                        .WithMany("CheckStatusHistories")
                        .HasForeignKey("CheckCollectionId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckDepositModel", "CheckDeposit")
                        .WithMany("CheckStatusHistories")
                        .HasForeignKey("CheckDepositId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckRejectModel", "CheckReject")
                        .WithMany("CheckStatusHistories")
                        .HasForeignKey("CheckRejectId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckReturnModel", "CheckReturn")
                        .WithMany("CheckStatusHistories")
                        .HasForeignKey("CheckReturnId");

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckStatusModel", "CheckStatusFrom")
                        .WithMany("CheckStatusFromHistories")
                        .HasForeignKey("CheckStatusFromId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckStatusModel", "CheckStatusTo")
                        .WithMany("CheckStatusToHistories")
                        .HasForeignKey("CheckStatusToId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckTreasuryVoucherModel", "CheckTreasuryVoucher")
                        .WithMany("CheckStatusHistories")
                        .HasForeignKey("CheckTreasuryVoucherId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("CheckClear");

                    b.Navigation("CheckCollection");

                    b.Navigation("CheckDeposit");

                    b.Navigation("CheckReject");

                    b.Navigation("CheckReturn");

                    b.Navigation("CheckStatusFrom");

                    b.Navigation("CheckStatusTo");

                    b.Navigation("CheckTreasuryVoucher");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckTreasuryVoucherModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.BankAccountModel", "BankAccount")
                        .WithMany("CheckTreasuryVouchers")
                        .HasForeignKey("BankAccountId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.BankModel", "Bank")
                        .WithMany("CheckTreasuryVouchers")
                        .HasForeignKey("BankId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.BeneficiaryTypeModel", "BeneficiaryType")
                        .WithMany("CheckTreasuryVouchers")
                        .HasForeignKey("BeneficiaryTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckClearModel", "CheckClear")
                        .WithMany("CheckTreasuryVouchers")
                        .HasForeignKey("CheckClearId");

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckCollectionModel", "CheckCollection")
                        .WithMany("CheckTreasuryVouchers")
                        .HasForeignKey("CheckCollectionId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckDepositModel", "CheckDeposit")
                        .WithMany("CheckTreasuryVouchers")
                        .HasForeignKey("CheckDepositId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckRejectModel", "CheckReject")
                        .WithMany("CheckTreasuryVouchers")
                        .HasForeignKey("CheckRejectId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckReturnModel", "CheckReturn")
                        .WithMany("CheckTreasuryVouchers")
                        .HasForeignKey("CheckReturnId");

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckStatusModel", "CheckStatus")
                        .WithMany("CheckTreasuryVouchers")
                        .HasForeignKey("CheckStatusId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckVaultModel", "CheckVault")
                        .WithMany("CheckTreasuryVouchers")
                        .HasForeignKey("CheckVaultId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckVaultLocationModel", "CheckVaultLocation")
                        .WithMany("CheckTreasuryVouchers")
                        .HasForeignKey("CheckVaultLocationId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Sales.CustomerModel", "Customer")
                        .WithMany("CheckTreasuryVouchers")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.HR.EmployeeModel", "Employee")
                        .WithMany("CheckTreasuryVouchers")
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.TransactionTypeModel", "TransactionType")
                        .WithMany("CheckTreasuryVouchers")
                        .HasForeignKey("TransactionTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Purchases.VendorModel", "Vendor")
                        .WithMany("CheckTreasuryVouchers")
                        .HasForeignKey("VendorId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.Navigation("Bank");

                    b.Navigation("BankAccount");

                    b.Navigation("BeneficiaryType");

                    b.Navigation("CheckClear");

                    b.Navigation("CheckCollection");

                    b.Navigation("CheckDeposit");

                    b.Navigation("CheckReject");

                    b.Navigation("CheckReturn");

                    b.Navigation("CheckStatus");

                    b.Navigation("CheckVault");

                    b.Navigation("CheckVaultLocation");

                    b.Navigation("Customer");

                    b.Navigation("Employee");

                    b.Navigation("TransactionType");

                    b.Navigation("Vendor");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckVaultLocationModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckVaultModel", "CheckVault")
                        .WithMany("CheckVaultLocations")
                        .HasForeignKey("CheckVaultId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("CheckVault");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.CashManagement.CashTreasuryVoucherModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.BeneficiaryTypeModel", "BeneficiaryType")
                        .WithMany("CashTreasuryVouchers")
                        .HasForeignKey("BeneficiaryTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Sales.CustomerModel", "Customer")
                        .WithMany("CashTreasuryVouchers")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Treasury.CashManagement.DrawerModel", "Drawer")
                        .WithMany("CashTreasuryVouchers")
                        .HasForeignKey("DrawerId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Treasury.CashManagement.DrawerLocationModel", "DrawerLocation")
                        .WithMany("CashTreasuryVouchers")
                        .HasForeignKey("DrawerLocationId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.HR.EmployeeModel", "Employee")
                        .WithMany("CashTreasuryVouchers")
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.TransactionTypeModel", "TransactionType")
                        .WithMany("CashTreasuryVouchers")
                        .HasForeignKey("TransactionTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Purchases.VendorModel", "Vendor")
                        .WithMany("CashTreasuryVouchers")
                        .HasForeignKey("VendorId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.Navigation("BeneficiaryType");

                    b.Navigation("Customer");

                    b.Navigation("Drawer");

                    b.Navigation("DrawerLocation");

                    b.Navigation("Employee");

                    b.Navigation("TransactionType");

                    b.Navigation("Vendor");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.CashManagement.DrawerLocationModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Treasury.CashManagement.DrawerModel", "Drawer")
                        .WithMany("DrawerLocations")
                        .HasForeignKey("DrawerId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("Drawer");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.TreasuryLineModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.BankTransferManagement.BankTransferTreasuryVoucherModel", "BankTransferTreasuryVoucher")
                        .WithMany("TreasuryLines")
                        .HasForeignKey("BankTransferTreasuryVoucherId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Purchases.BillModel", "Bill")
                        .WithMany("TreasuryLines")
                        .HasForeignKey("BillId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Purchases.BillReturnModel", "BillReturn")
                        .WithMany("TreasuryLines")
                        .HasForeignKey("BillReturnId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Treasury.CashManagement.CashTreasuryVoucherModel", "CashTreasuryVoucher")
                        .WithMany("TreasuryLines")
                        .HasForeignKey("CashTreasuryVoucherId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckTreasuryVoucherModel", "CheckTreasuryVoucher")
                        .WithMany("TreasuryLines")
                        .HasForeignKey("CheckTreasuryVoucherId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Treasury.ExpensesModel", "Expenses")
                        .WithMany("TreasuryLines")
                        .HasForeignKey("ExpensesId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Sales.InvoiceModel", "Invoice")
                        .WithMany("TreasuryLines")
                        .HasForeignKey("InvoiceId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Sales.InvoiceReturnModel", "InvoiceReturn")
                        .WithMany("TreasuryLines")
                        .HasForeignKey("InvoiceReturnId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.Navigation("BankTransferTreasuryVoucher");

                    b.Navigation("Bill");

                    b.Navigation("BillReturn");

                    b.Navigation("CashTreasuryVoucher");

                    b.Navigation("CheckTreasuryVoucher");

                    b.Navigation("Expenses");

                    b.Navigation("Invoice");

                    b.Navigation("InvoiceReturn");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.User.RefreshTokenModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.User.UserModel", "User")
                        .WithMany("RefreshTokens")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.User.ScreensAccessProfileDetailsModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.User.ScreensAccessProfileModel", "ScreensAccessProfile")
                        .WithMany("ScreensAccessProfileDetails")
                        .HasForeignKey("ScreensAccessProfileId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("ScreensAccessProfile");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.User.ScreensAccessProfileModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.User.UserModel", null)
                        .WithMany("ScreensAccessProfiles")
                        .HasForeignKey("UserModelId");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.User.SettingModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.User.UserModel", "User")
                        .WithOne("Setting")
                        .HasForeignKey("SimpleBooks.Models.Model.User.SettingModel", "UserId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.User.UserModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.User.ScreensAccessProfileModel", "ScreensAccessProfile")
                        .WithMany("Users")
                        .HasForeignKey("ScreensAccessProfileId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("ScreensAccessProfile");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.InventoryModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Purchases.BillModel", "Bill")
                        .WithMany("Inventories")
                        .HasForeignKey("BillId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Purchases.BillReturnModel", "BillReturn")
                        .WithMany("Inventories")
                        .HasForeignKey("BillReturnId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Sales.InvoiceModel", "Invoice")
                        .WithMany("Inventories")
                        .HasForeignKey("InvoiceId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Sales.InvoiceReturnModel", "InvoiceReturn")
                        .WithMany("Inventories")
                        .HasForeignKey("InvoiceReturnId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Warehouse.ProductModel", "Product")
                        .WithMany("Inventories")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Warehouse.UnitModel", "ProductUnit")
                        .WithMany("Inventories")
                        .HasForeignKey("ProductUnitId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Warehouse.StoreModel", "Store")
                        .WithMany("Inventories")
                        .HasForeignKey("StoreId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.TransactionTypeModel", "TransactionType")
                        .WithMany("Inventories")
                        .HasForeignKey("TransactionTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("Bill");

                    b.Navigation("BillReturn");

                    b.Navigation("Invoice");

                    b.Navigation("InvoiceReturn");

                    b.Navigation("Product");

                    b.Navigation("ProductUnit");

                    b.Navigation("Store");

                    b.Navigation("TransactionType");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.InventoryTaxModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Warehouse.InventoryModel", "Inventory")
                        .WithMany("InventoryTaxes")
                        .HasForeignKey("InventoryId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Tax.TaxSubTypeModel", "TaxSubType")
                        .WithMany("InventoryTaxes")
                        .HasForeignKey("TaxSubTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Tax.TaxTypeModel", "TaxType")
                        .WithMany("InventoryTaxes")
                        .HasForeignKey("TaxTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("Inventory");

                    b.Navigation("TaxSubType");

                    b.Navigation("TaxType");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.ProductModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Warehouse.ProductCategoryModel", "ProductCategory")
                        .WithMany("Products")
                        .HasForeignKey("ProductCategoryId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Warehouse.ProductTypeModel", "ProductType")
                        .WithMany("Products")
                        .HasForeignKey("ProductTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.HasOne("SimpleBooks.Models.Model.Warehouse.UnitModel", null)
                        .WithMany("Products")
                        .HasForeignKey("UnitModelId");

                    b.Navigation("ProductCategory");

                    b.Navigation("ProductType");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.ProductTaxModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Warehouse.ProductModel", "Product")
                        .WithMany("ProductTaxes")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Tax.TaxSubTypeModel", "TaxSubType")
                        .WithMany("ProductTaxes")
                        .HasForeignKey("TaxSubTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Tax.TaxTypeModel", "TaxType")
                        .WithMany("ProductTaxes")
                        .HasForeignKey("TaxTypeId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("TaxSubType");

                    b.Navigation("TaxType");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.ProductUnitModel", b =>
                {
                    b.HasOne("SimpleBooks.Models.Model.Warehouse.ProductModel", "Product")
                        .WithMany("ProductUnits")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.HasOne("SimpleBooks.Models.Model.Warehouse.UnitModel", "ProductUnit")
                        .WithMany("ProductUnits")
                        .HasForeignKey("ProductUnitId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("ProductUnit");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.BeneficiaryTypeModel", b =>
                {
                    b.Navigation("BankTransferTreasuryVouchers");

                    b.Navigation("CashTreasuryVouchers");

                    b.Navigation("CheckTreasuryVouchers");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.HR.EmployeeModel", b =>
                {
                    b.Navigation("BankTransferTreasuryVouchers");

                    b.Navigation("CashTreasuryVouchers");

                    b.Navigation("CheckTreasuryVouchers");

                    b.Navigation("Customers");

                    b.Navigation("InvoiceReturns");

                    b.Navigation("Invoices");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Purchases.BillModel", b =>
                {
                    b.Navigation("Inventories");

                    b.Navigation("TreasuryLines");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Purchases.BillReturnModel", b =>
                {
                    b.Navigation("Inventories");

                    b.Navigation("TreasuryLines");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Purchases.VendorModel", b =>
                {
                    b.Navigation("BankTransferTreasuryVouchers");

                    b.Navigation("BillReturns");

                    b.Navigation("Bills");

                    b.Navigation("CashTreasuryVouchers");

                    b.Navigation("CheckTreasuryVouchers");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Purchases.VendorTypeModel", b =>
                {
                    b.Navigation("BillReturns");

                    b.Navigation("Bills");

                    b.Navigation("Vendors");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Sales.CustomerModel", b =>
                {
                    b.Navigation("BankTransferTreasuryVouchers");

                    b.Navigation("CashTreasuryVouchers");

                    b.Navigation("CheckTreasuryVouchers");

                    b.Navigation("InvoiceReturns");

                    b.Navigation("Invoices");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Sales.CustomerTypeModel", b =>
                {
                    b.Navigation("Customers");

                    b.Navigation("InvoiceReturns");

                    b.Navigation("Invoices");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Sales.InvoiceModel", b =>
                {
                    b.Navigation("Inventories");

                    b.Navigation("TreasuryLines");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Sales.InvoiceReturnModel", b =>
                {
                    b.Navigation("Inventories");

                    b.Navigation("TreasuryLines");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Tax.TaxSubTypeModel", b =>
                {
                    b.Navigation("InventoryTaxes");

                    b.Navigation("ProductTaxes");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Tax.TaxTypeModel", b =>
                {
                    b.Navigation("InventoryTaxes");

                    b.Navigation("ProductTaxes");

                    b.Navigation("TaxSubTypes");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.TransactionTypeModel", b =>
                {
                    b.Navigation("BankTransferTreasuryVouchers");

                    b.Navigation("CashTreasuryVouchers");

                    b.Navigation("CheckTreasuryVouchers");

                    b.Navigation("Inventories");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.BankAccountModel", b =>
                {
                    b.Navigation("BankTransferTreasuryVouchers");

                    b.Navigation("CheckDeposits");

                    b.Navigation("CheckTreasuryVouchers");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.BankModel", b =>
                {
                    b.Navigation("BankAccounts");

                    b.Navigation("BankTransferTreasuryVouchers");

                    b.Navigation("CheckDeposits");

                    b.Navigation("CheckTreasuryVouchers");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.BankTransferManagement.BankTransferTreasuryVoucherModel", b =>
                {
                    b.Navigation("TreasuryLines");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckClearModel", b =>
                {
                    b.Navigation("CheckStatusHistories");

                    b.Navigation("CheckTreasuryVouchers");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckCollectionModel", b =>
                {
                    b.Navigation("CheckStatusHistories");

                    b.Navigation("CheckTreasuryVouchers");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckDepositModel", b =>
                {
                    b.Navigation("CheckStatusHistories");

                    b.Navigation("CheckTreasuryVouchers");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckRejectModel", b =>
                {
                    b.Navigation("CheckStatusHistories");

                    b.Navigation("CheckTreasuryVouchers");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckReturnModel", b =>
                {
                    b.Navigation("CheckStatusHistories");

                    b.Navigation("CheckTreasuryVouchers");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckStatusModel", b =>
                {
                    b.Navigation("CheckStatusFromHistories");

                    b.Navigation("CheckStatusToHistories");

                    b.Navigation("CheckTreasuryVouchers");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckTreasuryVoucherModel", b =>
                {
                    b.Navigation("CheckStatusHistories");

                    b.Navigation("TreasuryLines");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckVaultLocationModel", b =>
                {
                    b.Navigation("CheckReturns");

                    b.Navigation("CheckTreasuryVouchers");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement.CheckVaultModel", b =>
                {
                    b.Navigation("CheckReturns");

                    b.Navigation("CheckTreasuryVouchers");

                    b.Navigation("CheckVaultLocations");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.CashManagement.CashTreasuryVoucherModel", b =>
                {
                    b.Navigation("TreasuryLines");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.CashManagement.DrawerLocationModel", b =>
                {
                    b.Navigation("CashTreasuryVouchers");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.CashManagement.DrawerModel", b =>
                {
                    b.Navigation("CashTreasuryVouchers");

                    b.Navigation("DrawerLocations");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.ExpensesModel", b =>
                {
                    b.Navigation("TreasuryLines");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Treasury.PaymentTermModel", b =>
                {
                    b.Navigation("BillReturns");

                    b.Navigation("Bills");

                    b.Navigation("Customers");

                    b.Navigation("InvoiceReturns");

                    b.Navigation("Invoices");

                    b.Navigation("Vendors");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.User.ScreensAccessProfileModel", b =>
                {
                    b.Navigation("ScreensAccessProfileDetails");

                    b.Navigation("Users");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.User.UserModel", b =>
                {
                    b.Navigation("Employee")
                        .IsRequired();

                    b.Navigation("RefreshTokens");

                    b.Navigation("ScreensAccessProfiles");

                    b.Navigation("Setting")
                        .IsRequired();
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.InventoryModel", b =>
                {
                    b.Navigation("InventoryTaxes");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.ProductCategoryModel", b =>
                {
                    b.Navigation("Products");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.ProductModel", b =>
                {
                    b.Navigation("Inventories");

                    b.Navigation("ProductTaxes");

                    b.Navigation("ProductUnits");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.ProductTypeModel", b =>
                {
                    b.Navigation("Products");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.StoreModel", b =>
                {
                    b.Navigation("Inventories");
                });

            modelBuilder.Entity("SimpleBooks.Models.Model.Warehouse.UnitModel", b =>
                {
                    b.Navigation("Inventories");

                    b.Navigation("ProductUnits");

                    b.Navigation("Products");
                });
#pragma warning restore 612, 618
        }
    }
}
