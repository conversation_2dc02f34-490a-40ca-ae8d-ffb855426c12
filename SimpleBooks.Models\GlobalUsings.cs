﻿global using GMCadiomCore.Models.BaseModels;
global using GMCadiomCore.Models.Enumerations;
global using GMCadiomCore.Models.Model;
global using GMCadiomCore.Models.ModelValidation.Base;
global using GMCadiomCore.Models.ModelValidation.CustomAttribute;
global using GMCadiomCore.Models.ViewModel;
global using GMCadiomCore.Shared.Helper;
global using Newtonsoft.Json;
global using Newtonsoft.Json.Linq;
global using SimpleBooks.Models.BaseModels;
global using SimpleBooks.Models.Enumerations;
global using SimpleBooks.Models.Model.HR;
global using SimpleBooks.Models.Model.Purchases;
global using SimpleBooks.Models.Model.Sales;
global using SimpleBooks.Models.Model.Tax;
global using SimpleBooks.Models.Model.Treasury;
global using SimpleBooks.Models.Model.Treasury.BankManagement;
global using SimpleBooks.Models.Model.Treasury.BankManagement.BankTransferManagement;
global using SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement;
global using SimpleBooks.Models.Model.Treasury.CashManagement;
global using SimpleBooks.Models.Model.User;
global using SimpleBooks.Models.Model.Warehouse;
global using SimpleBooks.Models.ModelDTO.Tax;
global using SimpleBooks.Models.ModelDTO.Warehouse;
global using SimpleBooks.Models.ModelMapper.HR;
global using SimpleBooks.Models.ModelMapper.Purchases;
global using SimpleBooks.Models.ModelMapper.Sales;
global using SimpleBooks.Models.ModelMapper.Tax;
global using SimpleBooks.Models.ModelMapper.Treasury;
global using SimpleBooks.Models.ModelMapper.Treasury.BankManagement;
global using SimpleBooks.Models.ModelMapper.Treasury.BankManagement.BankTransferManagement;
global using SimpleBooks.Models.ModelMapper.Treasury.BankManagement.CheckManagement;
global using SimpleBooks.Models.ModelMapper.Treasury.CashManagement;
global using SimpleBooks.Models.ModelMapper.User;
global using SimpleBooks.Models.ModelMapper.Warehouse;
global using SimpleBooks.Models.ViewModel.BaseModels;
global using SimpleBooks.Models.ViewModel.HR.Employee;
global using SimpleBooks.Models.ViewModel.Purchases.Bill;
global using SimpleBooks.Models.ViewModel.Purchases.BillReturn;
global using SimpleBooks.Models.ViewModel.Purchases.PurchaseOrder;
global using SimpleBooks.Models.ViewModel.Purchases.PurchaseOrderLine;
global using SimpleBooks.Models.ViewModel.Purchases.Vendor;
global using SimpleBooks.Models.ViewModel.Purchases.VendorType;
global using SimpleBooks.Models.ViewModel.Sales.Customer;
global using SimpleBooks.Models.ViewModel.Sales.CustomerType;
global using SimpleBooks.Models.ViewModel.Sales.Invoice;
global using SimpleBooks.Models.ViewModel.Sales.InvoiceReturn;
global using SimpleBooks.Models.ViewModel.Sales.SalesOrder;
global using SimpleBooks.Models.ViewModel.Sales.SalesOrderLine;
global using SimpleBooks.Models.ViewModel.Tax.TaxSubType;
global using SimpleBooks.Models.ViewModel.Tax.TaxType;
global using SimpleBooks.Models.ViewModel.Treasury.BankManagement.Bank;
global using SimpleBooks.Models.ViewModel.Treasury.BankManagement.BankAccount;
global using SimpleBooks.Models.ViewModel.Treasury.BankManagement.BankTransferManagement.BankTransferTreasuryVoucher;
global using SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckClear;
global using SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckCollection;
global using SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckDeposit;
global using SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckReject;
global using SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckReturn;
global using SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckStatusHistory;
global using SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckTreasuryVoucher;
global using SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckVault;
global using SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckVaultLocation;
global using SimpleBooks.Models.ViewModel.Treasury.CashManagement.CashTreasuryVoucher;
global using SimpleBooks.Models.ViewModel.Treasury.CashManagement.Drawer;
global using SimpleBooks.Models.ViewModel.Treasury.CashManagement.DrawerLocation;
global using SimpleBooks.Models.ViewModel.Treasury.Expenses;
global using SimpleBooks.Models.ViewModel.Treasury.PaymentTerm;
global using SimpleBooks.Models.ViewModel.Treasury.TreasuryLine;
global using SimpleBooks.Models.ViewModel.Treasury.TreasuryVoucher;
global using SimpleBooks.Models.ViewModel.User.ScreensAccessProfile;
global using SimpleBooks.Models.ViewModel.User.ScreensAccessProfileDetails;
global using SimpleBooks.Models.ViewModel.User.Setting;
global using SimpleBooks.Models.ViewModel.User.User;
global using SimpleBooks.Models.ViewModel.Warehouse.Inventory;
global using SimpleBooks.Models.ViewModel.Warehouse.InventoryTax;
global using SimpleBooks.Models.ViewModel.Warehouse.Product;
global using SimpleBooks.Models.ViewModel.Warehouse.ProductCategory;
global using SimpleBooks.Models.ViewModel.Warehouse.ProductTax;
global using SimpleBooks.Models.ViewModel.Warehouse.ProductType;
global using SimpleBooks.Models.ViewModel.Warehouse.ProductUnit;
global using SimpleBooks.Models.ViewModel.Warehouse.Store;
global using SimpleBooks.Models.ViewModel.Warehouse.Unit;
global using SimpleBooks.Shared.Helper;
global using System.ComponentModel;
global using System.ComponentModel.DataAnnotations;
global using System.ComponentModel.DataAnnotations.Schema;
global using System.Text.RegularExpressions;
