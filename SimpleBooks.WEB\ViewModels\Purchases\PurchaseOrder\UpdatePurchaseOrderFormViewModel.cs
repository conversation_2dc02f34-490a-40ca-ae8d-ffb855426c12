﻿namespace SimpleBooks.WEB.ViewModels.Purchases.PurchaseOrder
{
    public class UpdatePurchaseOrderFormViewModel : UpdatePurchaseOrderViewModel
    {
        public IEnumerable<VendorDto> Vendors { get; set; } = Enumerable.Empty<VendorDto>();
        public IEnumerable<SelectListItem> VendorTypes { get; set; } = Enumerable.Empty<SelectListItem>();
        public IEnumerable<PaymentTermDto> PaymentTerms { get; set; } = Enumerable.Empty<PaymentTermDto>();
        public IEnumerable<ProductModel> Products { get; set; } = Enumerable.Empty<ProductModel>();
        public IEnumerable<SelectListItem> SelectiveProductUnits { get; set; } = Enumerable.Empty<SelectListItem>();
    }
}
