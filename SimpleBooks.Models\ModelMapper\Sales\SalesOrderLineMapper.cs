﻿namespace SimpleBooks.Models.ModelMapper.Sales
{
    public static class SalesOrderLineMapper
    {
        public static CreateSalesOrderLineViewModel ToCreateDto(this SalesOrderLineModel entity)
        {
            CreateSalesOrderLineViewModel viewModel = new CreateSalesOrderLineViewModel()
            {
                Quantity = entity.Quantity,
                Price = entity.Price,
                Amount = entity.Amount,
                UnitQtyRatio = entity.UnitQtyRatio,
                ProductId = entity.ProductId,
                ProductUnitId = entity.ProductUnitId,
                SalesOrderId = entity.SalesOrderId,
            };
            return viewModel;
        }

        public static SalesOrderLineModel ToEntity(this CreateSalesOrderLineViewModel entity)
        {
            SalesOrderLineModel model = new SalesOrderLineModel()
            {
                Quantity = entity.Quantity,
                Price = entity.Price,
                Amount = entity.Amount,
                UnitQtyRatio = entity.UnitQtyRatio,
                ProductId = entity.ProductId,
                ProductUnitId = entity.ProductUnitId,
                SalesOrderId = entity.SalesOrderId,
            };
            return model;
        }

        public static UpdateSalesOrderLineViewModel ToUpdateDto(this SalesOrderLineModel entity)
        {
            UpdateSalesOrderLineViewModel viewModel = new UpdateSalesOrderLineViewModel()
            {
                Id = entity.Id,
                Quantity = entity.Quantity,
                Price = entity.Price,
                Amount = entity.Amount,
                UnitQtyRatio = entity.UnitQtyRatio,
                ProductId = entity.ProductId,
                ProductUnitId = entity.ProductUnitId,
                SalesOrderId = entity.SalesOrderId,
            };
            return viewModel;
        }

        public static SalesOrderLineModel ToEntity(this UpdateSalesOrderLineViewModel entity)
        {
            SalesOrderLineModel model = new SalesOrderLineModel()
            {
                Id = entity.Id,
                Quantity = entity.Quantity,
                Price = entity.Price,
                Amount = entity.Amount,
                UnitQtyRatio = entity.UnitQtyRatio,
                ProductId = entity.ProductId,
                ProductUnitId = entity.ProductUnitId,
                SalesOrderId = entity.SalesOrderId,
            };
            return model;
        }
    }
}
