﻿namespace SimpleBooks.WEB.Controllers.Business.Treasury.BankManagement.CheckManagement
{
    public class CheckClearController : BaseBusinessController<
        CheckClearModel,
        CheckClearModel,
        CreateCheckClearViewModel,
        UpdateCheckClearViewModel,
        IndexCheckClearFormViewModel,
        CreateCheckClearFormViewModel,
        UpdateCheckClearFormViewModel>
    {
        private readonly ICheckClearService _checkClearService;

        public CheckClearController(ICheckClearService checkClearService) : base(checkClearService)
        {
            _checkClearService = checkClearService;
        }

        [FixCheckClear]
        public override async Task<IActionResult> Create(CreateCheckClearFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FullFillViewAsync(model);

            try
            {
                var result = await _checkClearService.AddAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("checkClear", ex.Message);
                return await FullFillViewAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        public override async Task<IActionResult> Update(Ulid id)
        {
            CheckClearModel? entity = await _checkClearService.GetByIdAsync(id);

            if (entity is null)
                return NotFound();

            JsonSerializerSettings jsonSerializerSettings = new JsonSerializerSettings()
            {
                ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
            };
            UpdateCheckClearFormViewModel viewModel = new UpdateCheckClearFormViewModel()
            {
                Id = entity.Id,
                ClearDate = entity.ClearDate,
                SelectedCheckTreasuryVouchers = entity.CheckTreasuryVouchers.Select(x => x.Id).ToList(),
                AllCheckTreasuryVouchers = await _checkClearService.SelectiveCheckTreasuryVouchers().GetDataOrThrowIfNullAsync(),
                SelectedChecksJson = JsonConvert.SerializeObject(entity.CheckTreasuryVouchers, jsonSerializerSettings),
            };

            return View(viewModel);
        }

        [FixCheckClear]
        public override async Task<IActionResult> Update(UpdateCheckClearFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FailedActionResultAsync(model);

            try
            {
                var result = await _checkClearService.UpdateAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("checkClear", ex.Message);
                return await FailedActionResultAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        protected override async Task<IActionResult> FullFillViewAsync(CreateCheckClearFormViewModel model)
        {
            model.AllCheckTreasuryVouchers = await _checkClearService.SelectiveCheckTreasuryVouchers().GetDataOrThrowIfNullAsync();
            return await Task.FromResult(View(model));
        }

        protected override async Task<IActionResult> FailedActionResultAsync(UpdateCheckClearFormViewModel model)
        {
            model.AllCheckTreasuryVouchers = await _checkClearService.SelectiveCheckTreasuryVouchers().GetDataOrThrowIfNullAsync();
            return await Task.FromResult(View(model));
        }
    }
}
