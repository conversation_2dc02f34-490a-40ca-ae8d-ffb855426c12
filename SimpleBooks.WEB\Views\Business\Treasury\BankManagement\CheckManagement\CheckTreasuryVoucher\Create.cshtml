﻿@model CreateCheckTreasuryVoucherFormViewModel

@{
    ViewData["Title"] = "Add Check Treasury Voucher";
}

<h5>
    <i class="bi bi-plus-circle-dotted"></i>
    Add a new Check Treasury Voucher
</h5>

<form asp-controller="CheckTreasuryVoucher" enctype="multipart/form-data">
    <div asp-validation-summary="All" class="text-danger" data-validation-summary="true"></div>
    <div class="row">
        <div class="col-md-4 mt-2">
            <div class="form-group">
                <label asp-for="TransactionDate" class="form-label mt-2"></label>
                <input type="date" class="form-control" asp-for="TransactionDate" placeholder="Transaction Date" value="@DateTime.Now.ToString("yyyy-MM-dd")">
                <span asp-validation-for="TransactionDate" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="BeneficiaryTypeId" class="form-label mt-2"></label>
                <select class="form-select" asp-for="BeneficiaryTypeId"
                        data-placeholder="Select a beneficiary type" data-minimum-results-for-search="Infinity" onchange="handler.onBeneficiaryTypeChanged(this.value)">
                    <option value=""></option>
                    @foreach (var item in Model.BeneficiaryTypes)
                    {
                        <option value="@item.Value">@item.Name</option>
                    }
                </select>
                <span asp-validation-for="BeneficiaryTypeId" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="BeneficiaryId" class="form-label mt-2"></label>
                <select class="form-select" asp-for="BeneficiaryId"
                        data-placeholder="Select a beneficiary type" data-minimum-results-for-search="Infinity" onchange="handler.onBeneficiaryChanged(this.value)">
                    <option value=""></option>
                    @if (Model.BeneficiaryTypeId == BeneficiaryTypeEnumeration.Vendor.Value)
                    {
                        foreach (var item in Model.Vendors)
                        {
                            <option value="@item.Value">@item.Text</option>
                        }
                    }
                    else if (Model.BeneficiaryTypeId == BeneficiaryTypeEnumeration.Customer.Value)
                    {
                        foreach (var item in Model.Customers)
                        {
                            <option value="@item.Value">@item.Text</option>
                        }
                    }
                    else if (Model.BeneficiaryTypeId == BeneficiaryTypeEnumeration.Employee.Value)
                    {
                        foreach (var item in Model.Employees)
                        {
                            <option value="@item.Value">@item.Text</option>
                        }
                    }
                </select>
                <span asp-validation-for="BeneficiaryId" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="TransactionTypeId" class="form-label mt-2"></label>
                <select class="form-select" asp-for="TransactionTypeId"
                        data-placeholder="Select a transaction type" data-minimum-results-for-search="Infinity" onchange="handler.onTransactionTypeChanged(this.value)">
                    <option value=""></option>
                    @foreach (var item in Model.TransactionTypes)
                    {
                        <option value="@item.Value">@item.Name</option>
                    }
                </select>
                <span asp-validation-for="TransactionTypeId" class="text-danger"></span>
            </div>
            <div class="CheckSector" style="display:@(Model.TransactionTypeId == TransactionTypeEnumeration.TreasuryCheckIn.Value ? "block" : "none")">
                <div class="form-group">
                    <label asp-for="CheckVaultId" class="form-label mt-2"></label>
                    <select class="form-select" asp-for="CheckVaultId"
                            data-placeholder="Select a check vault" data-minimum-results-for-search="Infinity" onchange="handler.onCheckVaultChanged(this.value)">
                        <option value=""></option>
                        @foreach (var item in Model.CheckVaults)
                        {
                            <option value="@item.Value">@item.Text</option>
                        }
                    </select>
                    <span asp-validation-for="CheckVaultId" class="text-danger"></span>
                </div>
                <div class="form-group">
                    <label asp-for="CheckVaultLocationId" class="form-label mt-2"></label>
                    <select class="form-select" asp-for="CheckVaultLocationId"
                            data-placeholder="Select a check vault location" data-minimum-results-for-search="Infinity">
                        <option value=""></option>
                        @foreach (var item in Model.CheckVaultLocations.Where(x => x.CheckVaultId == Model.CheckVaultId))
                        {
                            <option value="@item.Id">@item.CheckVaultLocationCurrency - @item.CheckVaultLocationNumber</option>
                        }
                    </select>
                    <span asp-validation-for="CheckVaultLocationId" class="text-danger"></span>
                </div>
            </div>
            <div class="BankSector" style="display:@(Model.TransactionTypeId == TransactionTypeEnumeration.TreasuryCheckOut.Value ? "block" : "none")">
                <div class="form-group">
                    <label asp-for="BankId" class="form-label mt-2"></label>
                    <select class="form-select" asp-for="BankId"
                            data-placeholder="Select a bank" data-minimum-results-for-search="Infinity" onchange="handler.onBankChanged(this.value)">
                        <option value=""></option>
                        @foreach (var item in Model.Banks)
                        {
                            <option value="@item.Value">@item.Text</option>
                        }
                    </select>
                    <span asp-validation-for="BankId" class="text-danger"></span>
                </div>
                <div class="form-group">
                    <label asp-for="BankAccountId" class="form-label mt-2"></label>
                    <select class="form-select" asp-for="BankAccountId"
                            data-placeholder="Select a bank account" data-minimum-results-for-search="Infinity">
                        <option value=""></option>
                        @foreach (var item in Model.BankAccounts.Where(x => x.BankId == Model.BankId))
                        {
                            <option value="@item.Id">@item.BankAccountCurrency - @item.BankAccountNumber</option>
                        }
                    </select>
                    <span asp-validation-for="BankAccountId" class="text-danger"></span>
                </div>
            </div>
            <div class="form-group">
                <label asp-for="CheckNumber" class="form-label mt-2"></label>
                <input type="number" class="form-control" asp-for="CheckNumber" placeholder="Check Number">
                <span asp-validation-for="CheckNumber" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="DueDate" class="form-label mt-2"></label>
                <input type="date" class="form-control" asp-for="DueDate" placeholder="Due Date" value="@DateTime.Now.ToString("yyyy-MM-dd")">
                <span asp-validation-for="DueDate" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="IssuerName" class="form-label mt-2"></label>
                <input type="text" class="form-control" asp-for="IssuerName" placeholder="Issuer Name">
                <span asp-validation-for="IssuerName" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="BearerName" class="form-label mt-2"></label>
                <input type="text" class="form-control" asp-for="BearerName" placeholder="Bearer Name">
                <span asp-validation-for="BearerName" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Amount" class="form-label mt-2"></label>
                <input type="number" class="form-control" asp-for="Amount" placeholder="Amount">
                <span asp-validation-for="Amount" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Note" class="form-label mt-2"></label>
                <input type="text" class="form-control" asp-for="Note" placeholder="Note">
                <span asp-validation-for="Note" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="RefranceNumber" class="form-label mt-2"></label>
                <input type="text" class="form-control" asp-for="RefranceNumber" placeholder="Refrance Number">
                <span asp-validation-for="RefranceNumber" class="text-danger"></span>
            </div>
        </div>
        <div class="col mt-3">
            <table id="TreasuryLinesDataGridView" class="table">
                <thead>
                    <tr>
                        <th>Treasury Line Type</th>
                        <th>Treasury Line Account</th>
                        <th>Amount</th>
                        <th></th>
                    </tr>
                </thead>
                <tbody>
                    @for (int i = 0; i < Model.TreasuryLines.Count; i++)
                    {
                        <tr data-row-index="@i">
                            <td>
                                <select class="form-select" asp-for="@Model.TreasuryLines[i].TreasuryLineTypeId" name="TreasuryLines[@i].TreasuryLineTypeId"
                                        data-placeholder="Select a treasury voucher line type" data-minimum-results-for-search="Infinity" onchange="handler.onTreasuryLineTypeChanged(this.value, @i)">
                                    <option value=""></option>
                                    @foreach (var item in Model.TreasuryLineTypes)
                                    {
                                        <option value="@item.Value">@item.Name</option>
                                    }
                                </select>
                                <span asp-validation-for="@Model.TreasuryLines[i].TreasuryLineTypeId" class="text-danger"></span>
                            </td>
                            <td>
                                <select class="form-select" asp-for="@Model.TreasuryLines[i].AccountId" name="TreasuryLines[@i].AccountId"
                                        data-placeholder="Select a account" data-minimum-results-for-search="Infinity">
                                    <option value=""></option>
                                    @if (Model.TreasuryLines[i].TreasuryLineTypeId == TreasuryLineTypeEnumeration.Expenses.Value)
                                    {
                                        foreach (var item in Model.Expenses)
                                        {
                                            <option value="@item.Value">@item.Text</option>
                                        }
                                    }
                                    else if (Model.TreasuryLines[i].TreasuryLineTypeId == TreasuryLineTypeEnumeration.Bill.Value)
                                    {
                                        foreach (var item in Model.Bills)
                                        {
                                            <option value="@item.Value">@item.Text</option>
                                        }
                                    }
                                    else if (Model.TreasuryLines[i].TreasuryLineTypeId == TreasuryLineTypeEnumeration.BillReturn.Value)
                                    {
                                        foreach (var item in Model.BillReturns)
                                        {
                                            <option value="@item.Value">@item.Text</option>
                                        }
                                    }
                                    else if (Model.TreasuryLines[i].TreasuryLineTypeId == TreasuryLineTypeEnumeration.Invoice.Value)
                                    {
                                        foreach (var item in Model.Invoices)
                                        {
                                            <option value="@item.Value">@item.Text</option>
                                        }
                                    }
                                    else if (Model.TreasuryLines[i].TreasuryLineTypeId == TreasuryLineTypeEnumeration.InvoiceReturn.Value)
                                    {
                                        foreach (var item in Model.InvoiceReturns)
                                        {
                                            <option value="@item.Value">@item.Text</option>
                                        }
                                    }
                                </select>
                                <span asp-validation-for="@Model.TreasuryLines[i].AccountId" class="text-danger"></span>
                            </td>
                            <td>
                                <input type="number" asp-for="@Model.TreasuryLines[i].Amount" name="TreasuryLines[@i].Amount" class="form-control" />
                                <span asp-validation-for="@Model.TreasuryLines[i].Amount" class="text-danger"></span>
                            </td>
                            <td>
                                <button type="button" class="btn btn-danger" onclick="removeRow(this)">Remove</button>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
            <button type="button" class="btn btn-primary" onclick="handler.addTreasuryLineRow()">Add Row</button>
        </div>
        <button type="submit" class="btn btn-primary mt-4">Save</button>
        <div class="row mt-3">
            @if (Model.CheckStatusHistories.Any())
            {
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th scope="col">Transaction Date</th>
                            <th scope="col">Check Status From</th>
                            <th scope="col">Check Status To</th>
                            <th scope="col">Note</th>
                            <th scope="col">Bank</th>
                            <th scope="col">Bank Account</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (CreateCheckStatusHistoryViewModel checkStatusHistory in Model.CheckStatusHistories)
                        {
                            <tr>
                                <td>@checkStatusHistory.TransactionDate.ToShortDateString()</td>
                                <td>@CheckStatusEnumeration.FromValue(checkStatusHistory.CheckStatusFromId).Name</td>
                                <td>@CheckStatusEnumeration.FromValue(checkStatusHistory.CheckStatusToId).Name</td>
                                <td>@checkStatusHistory.Note</td>
                                @*<td>@Model.Banks.FirstOrDefault(x => x.Value == checkStatusHistory.BankId.ToString())?.Text</td>
                                <td>@Model.BankAccounts.FirstOrDefault(x => x.Id == checkStatusHistory.BankAccountId)?.BankAccountCurrency</td>
 *@                            </tr>
                        }
                    </tbody>
                </table>
            }
        </div>
    </div>
</form>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script src="~/js/tools.js"></script>
    <script type="module">
        import { CheckTreasuryVoucherHandler } from '/js/business/Treasury/checkTreasuryVoucherHandler.js';
        const treasuryLineTypes = @Html.Raw(Json.Serialize(Model.TreasuryLineTypes));
        const checkVaults = @Html.Raw(Json.Serialize(Model.CheckVaults));
        const checkVaultLocations = @Html.Raw(Json.Serialize(Model.CheckVaultLocations));
        const banks = @Html.Raw(Json.Serialize(Model.Banks));
        const bankAccounts = @Html.Raw(Json.Serialize(Model.BankAccounts));
        const vendors = @Html.Raw(Json.Serialize(Model.Vendors));
        const customers = @Html.Raw(Json.Serialize(Model.Customers));
        const employees = @Html.Raw(Json.Serialize(Model.Employees));
        const expenses = @Html.Raw(Json.Serialize(Model.Expenses));
        const bills = @Html.Raw(Json.Serialize(Model.Bills));
        const billReturns =@Html.Raw(Json.Serialize(Model.BillReturns));
        const invoices = @Html.Raw(Json.Serialize(Model.Invoices));
        const invoiceReturns =@Html.Raw(Json.Serialize(Model.InvoiceReturns));

        window.handler = new CheckTreasuryVoucherHandler(
            treasuryLineTypes,
            checkVaults,
            checkVaultLocations,
            banks,
            bankAccounts,
            vendors,
            customers,
            employees,
            expenses,
            bills,
            billReturns,
            invoices,
            invoiceReturns);
    </script>
}
