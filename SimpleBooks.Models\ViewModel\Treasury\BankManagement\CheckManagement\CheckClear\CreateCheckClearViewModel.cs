﻿namespace SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckClear
{
    public class CreateCheckClearViewModel : BaseCreateViewModel, IEntityMapper<CheckClearModel, CreateCheckClearViewModel>
    {
        [CustomRequired]
        [DisplayName("Clear Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateTime ClearDate { get; set; }

        [CustomRequired]
        [DisplayName("Check Treasury Vouchers")]
        public virtual ICollection<Ulid> SelectedCheckTreasuryVouchers { get; set; } = new List<Ulid>();

        public CreateCheckClearViewModel ToDto(CheckClearModel entity) => entity.ToCreateDto();

        public CheckClearModel ToEntity() => CheckClearMapper.ToEntity(this);
    }
}
