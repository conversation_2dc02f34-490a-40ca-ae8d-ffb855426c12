﻿namespace SimpleBooks.Repositories.EF.Factory.Config.Sales
{
    public class SalesOrderConfiguration : IEntityTypeConfiguration<SalesOrderModel>
    {
        public void Configure(EntityTypeBuilder<SalesOrderModel> builder)
        {
            builder.<PERSON><PERSON><PERSON>(x => new { x.Id });

            builder.HasOne(d => d.Customer).WithMany(p => p.SalesOrders)
                .HasForeignKey(d => d.CustomerId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.CustomerType).WithMany(p => p.SalesOrders)
                .HasForeignKey(d => d.CustomerTypeId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.CustomerRep).WithMany(p => p.SalesOrders)
                .HasForeignKey(d => d.CustomerRepId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.PaymentTerm).WithMany(p => p.SalesOrders)
                .HasForeignKey(d => d.PaymentTermId)
                .OnDelete(DeleteBehavior.ClientCascade);
        }
    }
}
