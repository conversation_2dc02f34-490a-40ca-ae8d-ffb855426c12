﻿namespace SimpleBooks.Repositories.EF.Factory.Config.Purchases
{
    public class PurchaseOrderLineConfiguration : IEntityTypeConfiguration<PurchaseOrderLineModel>
    {
        public void Configure(EntityTypeBuilder<PurchaseOrderLineModel> builder)
        {
            builder.HasKey(x => new { x.Id });

            builder.HasOne(d => d.Product).WithMany(p => p.PurchaseOrderLines)
                .HasForeignKey(d => d.ProductId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.ProductUnit).WithMany(p => p.PurchaseOrderLines)
                .HasForeignKey(d => d.ProductUnitId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.PurchaseOrder).WithMany(p => p.PurchaseOrderLines)
                .HasForeignKey(d => d.PurchaseOrderId)
                .OnDelete(DeleteBehavior.ClientCascade);
        }
    }
}
