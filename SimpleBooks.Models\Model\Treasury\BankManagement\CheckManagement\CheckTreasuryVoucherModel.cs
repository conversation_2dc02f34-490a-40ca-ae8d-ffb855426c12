﻿namespace SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement
{
    [Table("CheckTreasuryVoucher")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<CheckTreasuryVoucherModel>))]
    public partial class CheckTreasuryVoucherModel : TreasuryVoucherModel
    {
        [CustomRequired]
        [DisplayName("Check Number")]
        public string CheckNumber { get; set; }
        [CustomRequired]
        [DisplayName("Due Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateTime DueDate { get; set; }
        [CustomRequired]
        [DisplayName("Issuer Name")]
        public string IssuerName { get; set; } = string.Empty; // For received checks
        [CustomRequired]
        [DisplayName("Bearer Name")]
        public string BearerName { get; set; } = string.Empty; // For issued checks
        [CustomRequired]
        [DisplayName("Check Status")]
        public Ulid CheckStatusId { get; set; }
        public virtual CheckStatusModel? CheckStatus { get; set; }

        [DisplayName("Check Vault")]
        public Ulid? CheckVaultId { get; set; }
        public virtual CheckVaultModel? CheckVault { get; set; }

        [DisplayName("Check Vault Location")]
        public Ulid? CheckVaultLocationId { get; set; }
        public virtual CheckVaultLocationModel? CheckVaultLocation { get; set; }

        [DisplayName("Bank")]
        public Ulid? BankId { get; set; }
        public virtual BankModel? Bank { get; set; }

        [DisplayName("Bank Account")]
        public Ulid? BankAccountId { get; set; }
        public virtual BankAccountModel? BankAccount { get; set; }

        [DisplayName("Check Deposit")]
        public Ulid? CheckDepositId { get; set; }
        public virtual CheckDepositModel? CheckDeposit { get; set; }

        [DisplayName("Check Collection")]
        public Ulid? CheckCollectionId { get; set; }
        public virtual CheckCollectionModel? CheckCollection { get; set; }

        [DisplayName("Check Reject")]
        public Ulid? CheckRejectId { get; set; }
        public virtual CheckRejectModel? CheckReject { get; set; }

        [DisplayName("Check Return")]
        public Ulid? CheckReturnId { get; set; }
        public virtual CheckReturnModel? CheckReturn { get; set; }

        [DisplayName("Check Clear")]
        public Ulid? CheckClearId { get; set; }
        public virtual CheckClearModel? CheckClear { get; set; }

        [CustomRequired]
        [DisplayName("Check Status Histories")]
        public virtual ICollection<CheckStatusHistoryModel> CheckStatusHistories { get; set; } = new List<CheckStatusHistoryModel>();
    }
}
