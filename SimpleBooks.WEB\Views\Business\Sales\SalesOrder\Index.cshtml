﻿@model IndexSalesOrderFormViewModel

@{
    ViewData["Title"] = "Sales Orders";
    List<IndexSalesOrderViewModel> SalesOrders = Model.MainList.Items;
}

<div class="row d-flex">
    <div class="me-3 pb-2">
        <a class="btn btn-secondary btn-lg float-start" asp-action="Create" method="get">
            <i class="bi bi-plus-circle-dotted"></i>
            Add Sales Order
        </a>
        <a class="btn btn-success btn-lg float-end" asp-action="ExportToExcel">
            <i class="bi bi-file-earmark-spreadsheet"></i>
            Export To Excel
        </a>
    </div>
    <form class="d-flex align-items-center" asp-action="Index">
        <input class="form-control me-3" type="search" asp-for="SearchValue" placeholder="Search">
        <button class="btn btn-info btn-lg" type="submit">Search</button>
    </form>
</div>

@if (!SalesOrders.Any())
{
    <div class="alert alert-warning mt-5">
        <h4 class="alert-heading">No Sales Orders!</h4>
        <p class="mb-0">No Sales Orders were added yet.</p>
    </div>
}
else
{
    <table class="table table-hover">
        <thead>
            <tr>
                <th scope="col">Sales Order Id</th>
                <th scope="col">Sales Order Date</th>
                <th scope="col">Customer</th>
                <th scope="col">Customer Type</th>
                <th scope="col">Customer Rep</th>
                <th scope="col">Payment Term</th>
                <th scope="col">Sales Order Due Date</th>
                <th scope="col">Amount</th>
                <th scope="col" class="">Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach (IndexSalesOrderViewModel SalesOrder in SalesOrders)
            {
                <tr>
                    <td>@SalesOrder.SalesOrderId</td>
                    <td>@SalesOrder.SalesOrderDate</td>
                    <td>@SalesOrder.CustomerName</td>
                    <td>@SalesOrder.CustomerTypeName</td>
                    <td>@SalesOrder.CustomerRepName</td>
                    <td>@SalesOrder.PaymentTermName</td>
                    <td>@SalesOrder.SalesOrderDueDate</td>
                    <td>@SalesOrder.Amount</td>

                    <td class="overflow-hidden align-middle">
                        <div class="d-flex justify-content-end">
                            <a class="btn btn-info rounded rounded-3 me-2" asp-action="Update" asp-route-id="@SalesOrder.Id">
                                <i class="bi bi-pencil-fill"></i>
                            </a>
                            <a href="javascript:;" class="btn btn-danger rounded rounded-3 js-delete" data-id="@SalesOrder.Id">
                                <i class="bi bi-trash3"></i>
                            </a>
                        </div>
                    </td>

                </tr>
            }
        </tbody>
    </table>

    if (Model != null && Model.MainList != null)
        @await Html.PartialAsync("_Pagination", Model)
}

@section Scripts
{
    <script src="~/js/SalesOrder-index.js" asp-append-version="true"></script>
}