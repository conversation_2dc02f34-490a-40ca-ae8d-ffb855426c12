﻿namespace SimpleBooks.WEB.Controllers.Business.Sales
{
    public class InvoiceController : BaseBusinessController<
        InvoiceModel,
        IndexInvoiceViewModel,
        CreateInvoiceViewModel,
        UpdateInvoiceViewModel,
        IndexInvoiceFormViewModel,
        CreateInvoiceFormViewModel,
        UpdateInvoiceFormViewModel>
    {
        private readonly IInvoiceService _invoiceService;

        public InvoiceController(IInvoiceService invoiceService) : base(invoiceService)
        {
            _invoiceService = invoiceService;
        }

        public override async Task<IActionResult> Create(CreateInvoiceFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FullFillViewAsync(model);

            try
            {
                var result = await _invoiceService.AddAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("invoice", ex.Message);
                return await FullFillViewAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        public override async Task<IActionResult> Update(Ulid id)
        {
            InvoiceModel? entity = await _invoiceService.GetByIdAsync(id);

            if (entity is null)
                return NotFound();

            UpdateInvoiceFormViewModel viewModel = new UpdateInvoiceFormViewModel()
            {
                Id = entity.Id,
                InvoiceId = entity.InvoiceId,
                InvoiceDate = entity.InvoiceDate,
                InvoiceDueDate = entity.InvoiceDueDate,
                CustomerId = entity.CustomerId,
                CustomerTypeId = entity.CustomerTypeId,
                CustomerRepId = entity.CustomerRepId,
                PaymentTermId = entity.PaymentTermId,
                Inventories = entity.Inventories.Select(x => x.ToUpdateDto()).ToList(),
                Customers = await _invoiceService.SelectiveCustomerListAsync().GetDataOrThrowIfNullAsync(),
                CustomerTypes = await _invoiceService.SelectiveCustomerTypeListAsync().ToSelectListItemAsync(),
                CustomerReps = await _invoiceService.SelectiveCustomerRepListAsync().ToSelectListItemAsync(),
                PaymentTerms = await _invoiceService.SelectivePaymentTermListAsync().GetDataOrThrowIfNullAsync(),
                Products = await _invoiceService.SelectiveProductListAsync().GetDataOrThrowIfNullAsync(),
                SelectiveProductUnits = await _invoiceService.SelectiveUnitListAsync().ToSelectListItemAsync(),
                SelectiveStores = await _invoiceService.SelectiveStoreListAsync().ToSelectListItemAsync(),
                TaxTypes = await _invoiceService.TaxTypeListAsync().GetDataOrThrowIfNullAsync(),
                TaxSubTypes = await _invoiceService.TaxSubTypeListAsync().GetDataOrThrowIfNullAsync(),
            };

            return View(viewModel);
        }

        public override async Task<IActionResult> Update(UpdateInvoiceFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FailedActionResultAsync(model);

            try
            {
                var result = await _invoiceService.UpdateAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("invoice", ex.Message);
                return await FailedActionResultAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        protected override async Task<IActionResult> FullFillViewAsync(CreateInvoiceFormViewModel model)
        {
            model.Customers = await _invoiceService.SelectiveCustomerListAsync().GetDataOrThrowIfNullAsync();
            model.CustomerTypes = await _invoiceService.SelectiveCustomerTypeListAsync().ToSelectListItemAsync();
            model.CustomerReps = await _invoiceService.SelectiveCustomerRepListAsync().ToSelectListItemAsync();
            model.PaymentTerms = await _invoiceService.SelectivePaymentTermListAsync().GetDataOrThrowIfNullAsync();
            model.Products = await _invoiceService.SelectiveProductListAsync().GetDataOrThrowIfNullAsync();
            model.SelectiveProductUnits = await _invoiceService.SelectiveUnitListAsync().ToSelectListItemAsync();
            model.SelectiveStores = await _invoiceService.SelectiveStoreListAsync().ToSelectListItemAsync();
            model.TaxTypes = await _invoiceService.TaxTypeListAsync().GetDataOrThrowIfNullAsync();
            model.TaxSubTypes = await _invoiceService.TaxSubTypeListAsync().GetDataOrThrowIfNullAsync();
            return await Task.FromResult(View(model));
        }

        protected override async Task<IActionResult> FailedActionResultAsync(UpdateInvoiceFormViewModel model)
        {
            model.Customers = await _invoiceService.SelectiveCustomerListAsync().GetDataOrThrowIfNullAsync();
            model.CustomerTypes = await _invoiceService.SelectiveCustomerTypeListAsync().ToSelectListItemAsync();
            model.CustomerReps = await _invoiceService.SelectiveCustomerRepListAsync().ToSelectListItemAsync();
            model.PaymentTerms = await _invoiceService.SelectivePaymentTermListAsync().GetDataOrThrowIfNullAsync();
            model.Products = await _invoiceService.SelectiveProductListAsync().GetDataOrThrowIfNullAsync();
            model.SelectiveProductUnits = await _invoiceService.SelectiveUnitListAsync().ToSelectListItemAsync();
            model.SelectiveStores = await _invoiceService.SelectiveStoreListAsync().ToSelectListItemAsync();
            model.TaxTypes = await _invoiceService.TaxTypeListAsync().GetDataOrThrowIfNullAsync();
            model.TaxSubTypes = await _invoiceService.TaxSubTypeListAsync().GetDataOrThrowIfNullAsync();
            return await Task.FromResult(View(model));
        }

        [HttpGet]
        public async Task<IActionResult> GetOpenSalesOrdersByCustomer(Ulid customerId)
        {
            try
            {
                var result = await _invoiceService.GetOpenSalesOrdersByCustomerAsync(customerId);
                if (result.IsSuccess)
                {
                    return Json(new { success = true, data = result.Data });
                }
                return Json(new { success = false, message = result.Message });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetOpenSalesOrderLines(Ulid salesOrderId)
        {
            try
            {
                var result = await _invoiceService.GetOpenSalesOrderLinesAsync(salesOrderId);
                if (result.IsSuccess)
                {
                    return Json(new { success = true, data = result.Data });
                }
                return Json(new { success = false, message = result.Message });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }
    }
}
