﻿namespace SimpleBooks.Repositories.EF.Repository.Treasury.BankManagement.CheckManagement
{
    internal class CheckClearRepository : SimpleBooksBaseRepository<CheckClearModel, CheckClearModel>
    {
        public CheckClearRepository(ApplicationDBContext context) : base(context)
        {
        }

        protected override Expression<Func<CheckClearModel, object>> OrderByColumn => x => x.Id;

        public override async Task<CheckClearModel?> AddAsync(CheckClearModel? model)
        {
            if (model == null)
                throw new Exception("Model cannot be null");

            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var newIds = model.CheckTreasuryVouchers.Select(x => x.Id).ToHashSet();

                var specs = new RepositorySpecifications<CheckTreasuryVoucherModel>
                {
                    Includes = x => x.Include(xx => xx.CheckStatusHistories),
                    SearchValue = x => newIds.Contains(x.Id) && x.CheckStatusId == CheckStatusEnumeration.Issued.Value,
                    IsTackable = true,
                };

                var selectedChecks = await GetQueryable(specs).ToListAsync();

                var missingIds = newIds.Except(selectedChecks.Select(x => x.Id)).ToList();
                if (missingIds.Any())
                    throw new Exception($"Invalid check(s): {string.Join(", ", missingIds)}");

                foreach (var check in selectedChecks)
                {
                    check.CheckStatusId = CheckStatusEnumeration.Cleared.Value;
                    check.CheckClearId = model.Id;

                    model.CheckStatusHistories.Add(new CheckStatusHistoryModel
                    {
                        TransactionDate = model.ClearDate,
                        Note = $"Check cleared on {model.ClearDate:dd/MM/yyyy}",
                        CheckStatusFromId = CheckStatusEnumeration.Issued.Value,
                        CheckStatusToId = CheckStatusEnumeration.Cleared.Value,
                        CheckTreasuryVoucherId = check.Id,
                        CheckClearId = model.Id
                    });
                }

                model.CheckTreasuryVouchers = selectedChecks;

                var result = await _context.Set<CheckClearModel>().AddAsync(model);
                await _context.SaveChangesAsync();
                return result.Entity;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public override async Task<CheckClearModel?> UpdateAsync(CheckClearModel? model)
        {
            if (model == null)
                throw new Exception("Model cannot be null");
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var spec = new RepositorySpecifications<CheckClearModel>
                {
                    Includes = x => x
                        .Include(d => d.CheckTreasuryVouchers)
                        .ThenInclude(c => c.CheckStatusHistories),
                    SearchValue = x => x.Id == model.Id,
                    IsTackable = true
                };

                var existingClearQuery = GetQueryable(spec);

                var existingClear = await existingClearQuery.FirstOrDefaultAsync();
                if (existingClear == null)
                    throw new Exception("Clear not found");
                var existingChecks = existingClear.CheckTreasuryVouchers.ToList();
                var newCheckIds = model.CheckTreasuryVouchers.Select(c => c.Id).ToHashSet();
                var oldCheckIds = existingChecks.Select(c => c.Id).ToHashSet();

                // 1. Checks to be removed
                var removedChecks = existingChecks.Where(c => !newCheckIds.Contains(c.Id)).ToList();

                foreach (var check in removedChecks)
                {
                    check.CheckStatusId = CheckStatusEnumeration.Issued.Value;
                    check.CheckClearId = null;

                    var collectionHistory = check.CheckStatusHistories
                        .FirstOrDefault(h => h.CheckClearId == existingClear.Id && h.CheckStatusToId == CheckStatusEnumeration.Cleared.Value);

                    if (collectionHistory != null)
                        _context.Set<CheckStatusHistoryModel>().Remove(collectionHistory);
                }

                // 2. Checks to be added
                var addedCheckIds = newCheckIds.Except(oldCheckIds).ToList();
                var newCheckSpecs = new RepositorySpecifications<CheckTreasuryVoucherModel>
                {
                    Includes = x => x.Include(xx => xx.CheckStatusHistories),
                    SearchValue = x => addedCheckIds.Contains(x.Id) && x.CheckStatusId == CheckStatusEnumeration.Issued.Value,
                    IsTackable = true
                };
                var addedChecks = GetQueryable(newCheckSpecs);

                foreach (var check in addedChecks)
                {
                    check.CheckStatusId = CheckStatusEnumeration.Cleared.Value;
                    check.CheckClearId = existingClear.Id;

                    check.CheckStatusHistories.Add(new CheckStatusHistoryModel
                    {
                        TransactionDate = model.ClearDate,
                        Note = $"Check cleared on {model.ClearDate:dd/MM/yyyy}",
                        CheckStatusFromId = CheckStatusEnumeration.Issued.Value,
                        CheckStatusToId = CheckStatusEnumeration.Cleared.Value,
                        CheckTreasuryVoucherId = check.Id,
                        CheckClearId = existingClear.Id
                    });
                }

                // 3. Remaining checks that were not changed
                var retainedChecks = existingChecks.Where(c => newCheckIds.Contains(c.Id)).ToList();

                // 4. Final update: fully replace the list with only the updated list
                existingClear.CheckTreasuryVouchers = retainedChecks.Concat(addedChecks).ToList();
                existingClear.ClearDate = model.ClearDate;

                var result = _context.Set<CheckClearModel>().Update(existingClear);
                await _context.SaveChangesAsync();
                return result.Entity;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }
    }
}
