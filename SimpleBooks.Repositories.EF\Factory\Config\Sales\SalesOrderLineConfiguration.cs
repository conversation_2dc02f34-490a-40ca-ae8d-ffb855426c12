﻿namespace SimpleBooks.Repositories.EF.Factory.Config.Sales
{
    public class SalesOrderLineConfiguration : IEntityTypeConfiguration<SalesOrderLineModel>
    {
        public void Configure(EntityTypeBuilder<SalesOrderLineModel> builder)
        {
            builder.<PERSON><PERSON><PERSON>(x => new { x.Id });

            builder.HasOne(d => d.Product).WithMany(p => p.SalesOrderLines)
                .HasForeignKey(d => d.ProductId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.ProductUnit).WithMany(p => p.SalesOrderLines)
                .HasForeignKey(d => d.ProductUnitId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.SalesOrder).WithMany(p => p.SalesOrderLines)
                .HasForeignKey(d => d.SalesOrderId)
                .OnDelete(DeleteBehavior.ClientCascade);
        }
    }
}
