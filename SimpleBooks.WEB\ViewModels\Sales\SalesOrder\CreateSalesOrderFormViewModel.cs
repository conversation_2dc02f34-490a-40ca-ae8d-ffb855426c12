﻿namespace SimpleBooks.WEB.ViewModels.Sales.SalesOrder
{
    public class CreateSalesOrderFormViewModel : CreateSalesOrderViewModel
    {
        public IEnumerable<CustomerDto> Customers { get; set; } = Enumerable.Empty<CustomerDto>();
        public IEnumerable<SelectListItem> CustomerTypes { get; set; } = Enumerable.Empty<SelectListItem>();
        public IEnumerable<SelectListItem> CustomerReps { get; set; } = Enumerable.Empty<SelectListItem>();
        public IEnumerable<PaymentTermDto> PaymentTerms { get; set; } = Enumerable.Empty<PaymentTermDto>();
        public IEnumerable<ProductModel> Products { get; set; } = Enumerable.Empty<ProductModel>();
        public IEnumerable<SelectListItem> SelectiveProductUnits { get; set; } = Enumerable.Empty<SelectListItem>();
    }
}
