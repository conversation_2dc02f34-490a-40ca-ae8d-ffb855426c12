﻿namespace SimpleBooks.Models.Model.Warehouse
{
    [Table("Unit")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<UnitModel>))]
    public class UnitModel : BaseWithoutTrackingModel
    {
        [CustomRequired]
        [DisplayName("Unit Name")]
        public string UnitName { get; set; }

        public virtual ICollection<ProductModel> Products { get; set; } = new List<ProductModel>();
        public virtual ICollection<ProductUnitModel> ProductUnits { get; set; } = new List<ProductUnitModel>();
        public virtual ICollection<InventoryModel> Inventories { get; set; } = new List<InventoryModel>();
        [DisplayName("Purchase Order Lines")]
        public virtual ICollection<PurchaseOrderLineModel> PurchaseOrderLines { get; set; } = new List<PurchaseOrderLineModel>();
        [DisplayName("Sales Order Lines")]
        public virtual ICollection<SalesOrderLineModel> SalesOrderLines { get; set; } = new List<SalesOrderLineModel>();
    }
}
