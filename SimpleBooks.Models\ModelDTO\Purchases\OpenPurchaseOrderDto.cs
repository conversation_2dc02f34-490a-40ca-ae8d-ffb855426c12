﻿namespace SimpleBooks.Models.ModelDTO.Purchases
{
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<OpenPurchaseOrderDto>))]
    public class OpenPurchaseOrderDto
    {
        public Ulid Id { get; set; }
        public string PurchaseOrderId { get; set; }
        public DateOnly PurchaseOrderDate { get; set; }
        public DateOnly PurchaseOrderDueDate { get; set; }
        public Ulid VendorId { get; set; }
        public Ulid? VendorTypeId { get; set; }
        public Ulid? PaymentTermId { get; set; }
        public virtual ICollection<OpenPurchaseOrderLineDto> OpenPurchaseOrderLines { get; set; } = new List<OpenPurchaseOrderLineDto>();
    }

    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<OpenPurchaseOrderLineDto>))]
    public class OpenPurchaseOrderLineDto
    {
        public Ulid Id { get; set; }
        public decimal Quantity { get; set; }
        public decimal Price { get; set; }
        public decimal Amount { get; set; }
        public decimal UnitQtyRatio { get; set; }
        public Ulid ProductId { get; set; }
        public Ulid ProductUnitId { get; set; }
        public Ulid PurchaseOrderId { get; set; }
    }
}
