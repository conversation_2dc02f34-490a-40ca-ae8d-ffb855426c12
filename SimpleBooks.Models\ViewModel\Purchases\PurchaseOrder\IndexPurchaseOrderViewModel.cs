﻿namespace SimpleBooks.Models.ViewModel.Purchases.PurchaseOrder
{
    public class IndexPurchaseOrderViewModel : BaseIdentityModel
    {
        [DisplayName("Purchase Order Id")]
        public string PurchaseOrderId { get; set; }
        [DisplayName("Purchase Order Date")]
        public DateOnly PurchaseOrderDate { get; set; }
        [DisplayName("Purchase Order Due Date")]
        public DateOnly PurchaseOrderDueDate { get; set; }
        [DisplayName("Vendor Name")]
        public string VendorName { get; set; }
        [DisplayName("Vendor Type Name")]
        public string VendorTypeName { get; set; }
        [DisplayName("Payment Term Name")]
        public string PaymentTermName { get; set; }
        [DisplayName("Amount")]
        public decimal Amount { get; set; }
    }
}
