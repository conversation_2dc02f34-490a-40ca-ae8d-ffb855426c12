﻿namespace SimpleBooks.Repositories.EF.Repository.Sales
{
    internal class SalesOrderRepository : SimpleBooksBaseRepository<SalesOrderModel, IndexSalesOrderViewModel>, ISalesOrderRepository<SalesOrderModel, IndexSalesOrderViewModel>
    {
        public SalesOrderRepository(ApplicationDBContext context) : base(context)
        {
        }

        protected override Expression<Func<IndexSalesOrderViewModel, string>>[] NamesOfSearchView => [x => x.SalesOrderId, x => x.CustomerName];
        protected override IQueryable<IndexSalesOrderViewModel> ViewQueryable => SalesOrderQueries.SalesOrderViewQuery(_context);
        protected override string ViewQuery => SalesOrderQueries.SalesOrderViewQuery(_context).ToQueryString();
        protected override Expression<Func<SalesOrderModel, string>> NameSelector => x => x.SalesOrderId;
        protected override Expression<Func<IndexSalesOrderViewModel, object>> OrderByColumn => x => x.Id;

        public async Task<IEnumerable<OpenSalesOrderDto>> GetOpenSalesOrdersByCustomerAsync(Ulid customerId)
        {
            if (customerId == Ulid.Empty)
                throw new ArgumentException("Customer ID cannot be empty.", nameof(customerId));

            RepositorySpecifications<SalesOrderModel> salesOrdersRepositorySpecifications = new RepositorySpecifications<SalesOrderModel>()
            {
                SearchValue = x => x.CustomerId == customerId,
                IsTackable = false,
                OrderBy = x => x.SalesOrderId,
                OrderByType = OrderByType.Descending,
            };

            var salesOrdersQuery = GetQueryable(salesOrdersRepositorySpecifications);

            var result = await salesOrdersQuery.Where(
                order => order.SalesOrderLines.Any(
                    line => line.Quantity > line.Inventories.Where(
                        inv => inv.ProductId == line.ProductId).Sum(
                        inv => inv.Quantity)
                        )
                )
                .Select(order => new OpenSalesOrderDto()
                {
                    Id = order.Id,
                    SalesOrderId = order.SalesOrderId,
                    SalesOrderDate = order.SalesOrderDate,
                    SalesOrderDueDate = order.SalesOrderDueDate,
                    CustomerId = order.CustomerId,
                    CustomerTypeId = order.CustomerTypeId,
                    CustomerRepId = order.CustomerRepId,
                    PaymentTermId = order.PaymentTermId,
                }).ToListAsync();

            return result;
        }

        public async Task<IEnumerable<OpenSalesOrderLineDto>> GetOpenSalesOrderLinesAsync(Ulid salesOrderId)
        {
            if (salesOrderId == Ulid.Empty)
                throw new ArgumentException("Sales Order ID cannot be empty.", nameof(salesOrderId));

            RepositorySpecifications<SalesOrderLineModel> salesOrderLinesRepositorySpecifications = new RepositorySpecifications<SalesOrderLineModel>()
            {
                SearchValue = x => x.SalesOrderId == salesOrderId,
                Includes = x => x.Include(xx => xx.Inventories),
                IsTackable = false,
                OrderBy = x => x.SalesOrderId,
                OrderByType = OrderByType.Descending,
            };

            var salesOrderLinesQuery = GetQueryable(salesOrderLinesRepositorySpecifications);

            var result = await salesOrderLinesQuery.Where(
                    line => line.Quantity > line.Inventories.Where(
                        inv => inv.ProductId == line.ProductId).Sum(
                        inv => inv.Quantity)
                        )
                .Select(line => new OpenSalesOrderLineDto()
                {
                    Id = line.Id,
                    Quantity = line.Quantity - line.Inventories.Where(inv => inv.ProductId == line.ProductId).Sum(inv => inv.Quantity),
                    Price = line.Price,
                    Amount = line.Amount,
                    UnitQtyRatio = line.UnitQtyRatio,
                    ProductId = line.ProductId,
                    ProductUnitId = line.ProductUnitId,
                    SalesOrderId = line.SalesOrderId
                }).ToListAsync();

            return result;
        }
    }
}
