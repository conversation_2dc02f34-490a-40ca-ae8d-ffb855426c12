﻿namespace SimpleBooks.Repositories.EF.Repository.Treasury.BankManagement.CheckManagement
{
    internal class CheckReturnRepository : SimpleBooksBaseRepository<CheckReturnModel, CheckReturnModel>
    {
        public CheckReturnRepository(ApplicationDBContext context) : base(context)
        {
        }

        protected override Expression<Func<CheckReturnModel, object>> OrderByColumn => x => x.Id;

        public override async Task<CheckReturnModel?> AddAsync(CheckReturnModel? model)
        {
            if (model == null)
                throw new Exception("Model cannot be null");
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var newIds = model.CheckTreasuryVouchers.Select(x => x.Id).ToHashSet();

                var specs = new RepositorySpecifications<CheckTreasuryVoucherModel>
                {
                    Includes = x => x.Include(xx => xx.CheckStatusHistories),
                    SearchValue = x => newIds.Contains(x.Id) && x.CheckStatusId == CheckStatusEnumeration.Rejected.Value,
                    IsTackable = true,
                };

                var selectedChecks = await GetQueryable(specs).ToListAsync();

                var missingIds = newIds.Except(selectedChecks.Select(x => x.Id)).ToList();
                if (missingIds.Any())
                    throw new Exception($"Invalid check(s): {string.Join(", ", missingIds)}");

                foreach (var check in selectedChecks)
                {
                    check.CheckStatusId = CheckStatusEnumeration.Returned.Value;
                    check.CheckReturnId = model.Id;

                    model.CheckStatusHistories.Add(new CheckStatusHistoryModel
                    {
                        TransactionDate = model.ReturnDate,
                        Note = $"Check returned on {model.ReturnDate:dd/MM/yyyy}",
                        CheckStatusFromId = CheckStatusEnumeration.Rejected.Value,
                        CheckStatusToId = CheckStatusEnumeration.Returned.Value,
                        CheckTreasuryVoucherId = check.Id,
                        CheckReturnId = model.Id
                    });
                }

                model.CheckTreasuryVouchers = selectedChecks;

                var result = await _context.Set<CheckReturnModel>().AddAsync(model);
                await _context.SaveChangesAsync();
                return result.Entity;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public override async Task<CheckReturnModel?> UpdateAsync(CheckReturnModel? model)
        {
            if (model == null)
                throw new Exception("Model cannot be null");
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var spec = new RepositorySpecifications<CheckReturnModel>
                {
                    Includes = x => x
                        .Include(d => d.CheckTreasuryVouchers)
                        .ThenInclude(c => c.CheckStatusHistories),
                    SearchValue = x => x.Id == model.Id,
                    IsTackable = true
                };

                var existingReturnQuery = GetQueryable(spec);

                var existingReturn = await existingReturnQuery.FirstOrDefaultAsync();
                if (existingReturn == null)
                    throw new Exception("Return not found");
                var existingChecks = existingReturn.CheckTreasuryVouchers.ToList();
                var newCheckIds = model.CheckTreasuryVouchers.Select(c => c.Id).ToHashSet();
                var oldCheckIds = existingChecks.Select(c => c.Id).ToHashSet();

                // 1. Checks to be removed
                var removedChecks = existingChecks.Where(c => !newCheckIds.Contains(c.Id)).ToList();

                foreach (var check in removedChecks)
                {
                    check.CheckStatusId = CheckStatusEnumeration.Rejected.Value;
                    check.CheckReturnId = null;

                    var returnHistory = check.CheckStatusHistories
                        .FirstOrDefault(h => h.CheckReturnId == existingReturn.Id && h.CheckStatusToId == CheckStatusEnumeration.Returned.Value);

                    if (returnHistory != null)
                        _context.Set<CheckStatusHistoryModel>().Remove(returnHistory);
                }

                // 2. Checks to be added
                var addedCheckIds = newCheckIds.Except(oldCheckIds).ToList();
                var newCheckSpecs = new RepositorySpecifications<CheckTreasuryVoucherModel>
                {
                    Includes = x => x.Include(xx => xx.CheckStatusHistories),
                    SearchValue = x => addedCheckIds.Contains(x.Id) && x.CheckStatusId == CheckStatusEnumeration.Rejected.Value,
                    IsTackable = true
                };
                var addedChecks = GetQueryable(newCheckSpecs);

                foreach (var check in addedChecks)
                {
                    check.CheckStatusId = CheckStatusEnumeration.Returned.Value;
                    check.CheckReturnId = existingReturn.Id;

                    check.CheckStatusHistories.Add(new CheckStatusHistoryModel
                    {
                        TransactionDate = model.ReturnDate,
                        Note = $"Check returned on {model.ReturnDate:dd/MM/yyyy}",
                        CheckStatusFromId = CheckStatusEnumeration.Rejected.Value,
                        CheckStatusToId = CheckStatusEnumeration.Returned.Value,
                        CheckTreasuryVoucherId = check.Id,
                        CheckReturnId = existingReturn.Id
                    });
                }

                // 3. Remaining checks that were not changed
                var retainedChecks = existingChecks.Where(c => newCheckIds.Contains(c.Id)).ToList();

                // 4. Final update: fully replace the list with only the updated list
                existingReturn.ReturnDate = model.ReturnDate;
                existingReturn.RefranceNumber = model.RefranceNumber;
                existingReturn.CheckVaultId = model.CheckVaultId;
                existingReturn.CheckVaultLocationId = model.CheckVaultLocationId;
                existingReturn.CheckTreasuryVouchers = retainedChecks.Concat(addedChecks).ToList();

                var result = _context.Set<CheckReturnModel>().Update(existingReturn);
                await _context.SaveChangesAsync();
                return result.Entity;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }
    }
}
