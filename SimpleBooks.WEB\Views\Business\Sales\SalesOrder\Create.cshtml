﻿@model CreateSalesOrderFormViewModel

@{
    ViewData["Title"] = "Add Sales Order";
}

<h5>
    <i class="bi bi-plus-circle-dotted"></i>
    Add a new Sales Order
</h5>

<form asp-controller="SalesOrder" enctype="multipart/form-data">
    <div asp-validation-summary="All" class="text-danger" data-validation-summary="true"></div>
    <div class="row">
        <div class="col-md-6 mt-2">
            <div class="form-group">
                <label asp-for="SalesOrderId" class="form-label mt-2"></label>
                <input type="text" class="form-control" asp-for="SalesOrderId" placeholder="SalesOrder Id">
                <span asp-validation-for="SalesOrderId" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="SalesOrderDate" class="form-label mt-2"></label>
                <input type="date" class="form-control" asp-for="SalesOrderDate" placeholder="SalesOrder Date" value="@DateTime.Now.ToString("yyyy-MM-dd")">
                <span asp-validation-for="SalesOrderDate" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="CustomerId" class="form-label mt-2"></label>
                <select class="form-select" asp-for="CustomerId"
                        data-placeholder="Select a customer" data-minimum-results-for-search="Infinity" onchange="handler.onCustomerChanged(this.value)">
                    <option value=""></option>
                    @foreach (var item in Model.Customers)
                    {
                        <option value="@item.Id">@item.CustomerName</option>
                    }
                </select>
                <span asp-validation-for="CustomerId" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="CustomerTypeId" class="form-label mt-2"></label>
                <select class="form-select" asp-for="CustomerTypeId" asp-items="Model.CustomerTypes"
                        data-placeholder="Select a customer type" data-minimum-results-for-search="Infinity">
                    <option value=""></option>
                </select>
                <span asp-validation-for="CustomerTypeId" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="CustomerRepId" class="form-label mt-2"></label>
                <select class="form-select" asp-for="CustomerRepId" asp-items="Model.CustomerReps"
                        data-placeholder="Select a customer rep" data-minimum-results-for-search="Infinity">
                    <option value=""></option>
                </select>
                <span asp-validation-for="CustomerRepId" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="PaymentTermId" class="form-label mt-2"></label>
                <select class="form-select" asp-for="PaymentTermId"
                        data-placeholder="Select a payment term" data-minimum-results-for-search="Infinity" onchange="handler.onPaymentTermChanged(this.value)">
                    <option value=""></option>
                    @foreach (var item in Model.PaymentTerms)
                    {
                        <option value="@item.Id">@item.PaymentTermName</option>
                    }
                </select>
                <span asp-validation-for="PaymentTermId" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="SalesOrderDueDate" class="form-label mt-2"></label>
                <input type="date" class="form-control" asp-for="SalesOrderDueDate" placeholder="SalesOrder Due Date" value="@DateTime.Now.ToString("yyyy-MM-dd")">
                <span asp-validation-for="SalesOrderDueDate" class="text-danger"></span>
            </div>
        </div>
        <div class="row">
            <h4 class="mt-4">Items</h4>
            <div class="form-group">
                <table id="SalesOrderLinesDataGridView" class="table">
                    <thead>
                        <tr>
                            <th>Product</th>
                            <th>Unit</th>
                            <th>Quantity</th>
                            <th>Price</th>
                            <th>Amount</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        @for (int i = 0; i < Model.SalesOrderLines.Count; i++)
                        {
                            <tr data-row-index="@i">
                                <td>
                                    <input type="hidden" asp-for="@Model.SalesOrderLines[i].UnitQtyRatio" name="SalesOrderLines[@i].UnitQtyRatio" />
                                    <select class="form-select" asp-for="@Model.SalesOrderLines[i].ProductId" name="SalesOrderLines[@i].ProductId" class="form-control"
                                            data-placeholder="Select a product" data-minimum-results-for-search="Infinity" onchange="handler.onProductChanged(this.value,@i)">
                                        <option value=""></option>
                                        @foreach (var item in Model.Products)
                                        {
                                            <option value="@item.Id">@item.ProductPurchasesDescription</option>
                                        }
                                    </select>
                                    <span asp-validation-for="@Model.SalesOrderLines[i].ProductId" class="text-danger"></span>
                                </td>
                                <td>
                                    <select class="form-select" asp-for="@Model.SalesOrderLines[i].ProductUnitId" asp-items="Model.SelectiveProductUnits" name="SalesOrderLines[@i].ProductUnitId" class="form-control"
                                            data-placeholder="Select a unit" data-minimum-results-for-search="Infinity">
                                        <option value=""></option>
                                    </select>
                                    <span asp-validation-for="@Model.SalesOrderLines[i].ProductUnitId" class="text-danger"></span>
                                </td>
                                <td>
                                    <input type="number" asp-for="@Model.SalesOrderLines[i].Quantity" name="SalesOrderLines[@i].Quantity" class="form-control" onchange="handler.onQuantityChanged(this)" />
                                    <span asp-validation-for="@Model.SalesOrderLines[i].Quantity" class="text-danger"></span>
                                </td>
                                <td>
                                    <input type="number" asp-for="@Model.SalesOrderLines[i].Price" name="SalesOrderLines[@i].Price" class="form-control" onchange="handler.onQuantityChanged(this)" />
                                    <span asp-validation-for="@Model.SalesOrderLines[i].Price" class="text-danger"></span>
                                </td>
                                <td>
                                    <input type="number" asp-for="@Model.SalesOrderLines[i].Amount" name="SalesOrderLines[@i].Amount" class="form-control" readonly />
                                    <span asp-validation-for="@Model.SalesOrderLines[i].Amount" class="text-danger"></span>
                                </td>
                                <td>
                                    <button type="button" class="btn btn-danger" onclick="removeRow(this)">Remove</button>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
                <button type="button" class="btn btn-primary" onclick="handler.addProductRow()">Add Row</button>
            </div>
        </div>
        <div class="col-md-6 mt-2">
            <button type="submit" class="btn btn-primary mt-4">Save</button>
        </div>
    </div>
</form>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
	<script src="~/js/tools.js"></script>
    <script type="module">
        import { SalesOrderHandler } from '/js/business/Sales/salesOrderHandler.js';
        const customers = @Html.Raw(Json.Serialize(Model.Customers));
        const paymentTerms = @Html.Raw(Json.Serialize(Model.PaymentTerms));
        const products = @Html.Raw(Json.Serialize(Model.Products));

        window.handler = new SalesOrderHandler(
            customers,
            paymentTerms,
            products
        );
    </script>
}
