﻿namespace SimpleBooks.Models.ViewModel.Purchases.PurchaseOrderLine
{
    public class UpdatePurchaseOrderLineViewModel : BaseUpdateViewModel, IEntityMapper<PurchaseOrderLineModel, UpdatePurchaseOrderLineViewModel>
    {
        [CustomRequired]
        [DisplayName("Quantity")]
        public decimal Quantity { get; set; }
        [CustomRequired]
        [DisplayName("Price")]
        public decimal Price { get; set; }
        [CustomRequired]
        [DisplayName("Amount")]
        public decimal Amount { get; set; }
        [CustomRequired]
        [DisplayName("Unit Qty Ratio")]
        public decimal UnitQtyRatio { get; set; }

        [CustomRequired]
        [DisplayName("Product")]
        public Ulid ProductId { get; set; }
        [CustomRequired]
        [DisplayName("Product Unit")]
        public Ulid ProductUnitId { get; set; }
        [CustomRequired]
        [DisplayName("PurchaseOrder")]
        public Ulid PurchaseOrderId { get; set; }

        public UpdatePurchaseOrderLineViewModel ToDto(PurchaseOrderLineModel entity) => entity.ToUpdateDto();

        public PurchaseOrderLineModel ToEntity() => PurchaseOrderLineMapper.ToEntity(this);
    }
}
