﻿namespace SimpleBooks.WEB.Controllers.Business.Purchases
{
    public class BillController : BaseBusinessController<
        BillModel,
        IndexBillViewModel,
        CreateBillViewModel,
        UpdateBillViewModel,
        IndexBillFormViewModel,
        CreateBillFormViewModel,
        UpdateBillFormViewModel>
    {
        private readonly IBillService _billService;

        public BillController(IBillService billService) : base(billService)
        {
            _billService = billService;
        }

        public override async Task<IActionResult> Create(CreateBillFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FullFillViewAsync(model);

            try
            {
                var result = await _billService.AddAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("bill", ex.Message);
                return await FullFillViewAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        public override async Task<IActionResult> Update(Ulid id)
        {
            BillModel? entity = await _billService.GetByIdAsync(id);

            if (entity is null)
                return NotFound();

            UpdateBillFormViewModel viewModel = new UpdateBillFormViewModel()
            {
                Id = entity.Id,
                BillId = entity.BillId,
                BillDate = entity.BillDate,
                BillDueDate = entity.BillDueDate,
                VendorId = entity.VendorId,
                VendorTypeId = entity.VendorTypeId,
                PaymentTermId = entity.PaymentTermId,
                Inventories = entity.Inventories.Select(x => x.ToUpdateDto()).ToList(),
                Vendors = await _billService.SelectiveVendorListAsync().GetDataOrThrowIfNullAsync(),
                VendorTypes = await _billService.SelectiveVendorTypeListAsync().ToSelectListItemAsync(),
                PaymentTerms = await _billService.SelectivePaymentTermListAsync().GetDataOrThrowIfNullAsync(),
                Products = await _billService.SelectiveProductListAsync().GetDataOrThrowIfNullAsync(),
                SelectiveProductUnits = await _billService.SelectiveUnitListAsync().ToSelectListItemAsync(),
                SelectiveStores = await _billService.SelectiveStoreListAsync().ToSelectListItemAsync(),
                TaxTypes = await _billService.TaxTypeListAsync().GetDataOrThrowIfNullAsync(),
                TaxSubTypes = await _billService.TaxSubTypeListAsync().GetDataOrThrowIfNullAsync(),
            };

            return View(viewModel);
        }

        public override async Task<IActionResult> Update(UpdateBillFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FailedActionResultAsync(model);

            try
            {
                var result = await _billService.UpdateAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("bill", ex.Message);
                return await FailedActionResultAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        protected override async Task<IActionResult> FullFillViewAsync(CreateBillFormViewModel model)
        {

            model.Vendors = await _billService.SelectiveVendorListAsync().GetDataOrThrowIfNullAsync();
            model.VendorTypes = await _billService.SelectiveVendorTypeListAsync().ToSelectListItemAsync();
            model.PaymentTerms = await _billService.SelectivePaymentTermListAsync().GetDataOrThrowIfNullAsync();
            model.Products = await _billService.SelectiveProductListAsync().GetDataOrThrowIfNullAsync();
            model.SelectiveProductUnits = await _billService.SelectiveUnitListAsync().ToSelectListItemAsync();
            model.SelectiveStores = await _billService.SelectiveStoreListAsync().ToSelectListItemAsync();
            model.TaxTypes = await _billService.TaxTypeListAsync().GetDataOrThrowIfNullAsync();
            model.TaxSubTypes = await _billService.TaxSubTypeListAsync().GetDataOrThrowIfNullAsync();
            return await Task.FromResult(View(model));
        }

        protected override async Task<IActionResult> FailedActionResultAsync(UpdateBillFormViewModel model)
        {
            model.Vendors = await _billService.SelectiveVendorListAsync().GetDataOrThrowIfNullAsync();
            model.VendorTypes = await _billService.SelectiveVendorTypeListAsync().ToSelectListItemAsync();
            model.PaymentTerms = await _billService.SelectivePaymentTermListAsync().GetDataOrThrowIfNullAsync();
            model.Products = await _billService.SelectiveProductListAsync().GetDataOrThrowIfNullAsync();
            model.SelectiveProductUnits = await _billService.SelectiveUnitListAsync().ToSelectListItemAsync();
            model.SelectiveStores = await _billService.SelectiveStoreListAsync().ToSelectListItemAsync();
            model.TaxTypes = await _billService.TaxTypeListAsync().GetDataOrThrowIfNullAsync();
            model.TaxSubTypes = await _billService.TaxSubTypeListAsync().GetDataOrThrowIfNullAsync();
            return await Task.FromResult(View(model));
        }

        [HttpGet]
        public async Task<IActionResult> GetOpenPurchaseOrdersByVendor(Ulid vendorId)
        {
            try
            {
                var result = await _billService.GetOpenPurchaseOrdersByVendorAsync(vendorId);
                if (result.IsSuccess)
                {
                    return Json(new { success = true, data = result.Data });
                }
                return Json(new { success = false, message = result.Message });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetOpenPurchaseOrderLines(Ulid purchaseOrderId)
        {
            try
            {
                var result = await _billService.GetOpenPurchaseOrderLinesAsync(purchaseOrderId);
                if (result.IsSuccess)
                {
                    return Json(new { success = true, data = result.Data });
                }
                return Json(new { success = false, message = result.Message });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }
    }
}
