﻿import { customerTools } from '/js/business/Sales/customerTools.js';
import { paymentTermTools } from '/js/business/Treasury/paymentTermTools.js';

export class InvoiceHandler {
    constructor(customers, paymentTerms) {
        this.customers = customers;
        this.paymentTerms = paymentTerms;
        this.openSalesOrders = [];
    }

    onCustomerChanged(customerId) {
        const customer = this.customers?.$values?.find(c => c.Id == customerId);
        if (customer) {
            customerTools.updateCustomer(customer);
            this.checkOpenSalesOrders(customerId);
        }
    }

    onPaymentTermChanged(paymentTermId) {
        const term = this.paymentTerms?.$values?.find(t => t.Id == paymentTermId);
        if (term) {
            paymentTermTools.updatePaymentTerm(term, "InvoiceDueDate");
        }
    }

    async checkOpenSalesOrders(customerId) {
        try {
            const url = `/Invoice/GetOpenSalesOrdersByCustomer?customerId=${customerId}`;
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const result = await response.json();
            if (result.success && result.data && result.data.$values && result.data.$values.length > 0) {
                this.openSalesOrders = result.data.$values;
                this.showSalesOrderSelection();
            } else {
                this.hideSalesOrderSelection();
            }
        } catch (error) {
            console.error('Error checking open sales orders:', error);
            console.error('Error details:', error.message);
        }
    }

    showSalesOrderSelection() {
        // Remove existing container if it exists
        let salesOrderContainer = document.getElementById('salesOrderContainer');
        if (salesOrderContainer) {
            salesOrderContainer.remove();
        }

        // Create new container
        salesOrderContainer = document.createElement('div');
        salesOrderContainer.id = 'salesOrderContainer';
        salesOrderContainer.className = 'alert alert-info mt-3';
        
        // Find the customer select element
        const customerSelect = document.querySelector('select[asp-for="CustomerId"]');
        
        if (customerSelect) {
            // Insert after the customer select's parent div
            const customerSelectParent = customerSelect.closest('.form-group');
            if (customerSelectParent) {
                customerSelectParent.insertAdjacentElement('afterend', salesOrderContainer);
            } else {
                // Fallback: insert after the customer select itself
                customerSelect.insertAdjacentElement('afterend', salesOrderContainer);
            }
        } else {
            // Fallback: append to the form
            const form = document.querySelector('form');
            if (form) {
                form.insertBefore(salesOrderContainer, form.firstChild);
            }
        }

        // Set the content
        salesOrderContainer.innerHTML = `
            <div class="row">
                <div class="col-md-12">
                    <h6><i class="bi bi-info-circle"></i> Open Sales Orders Found</h6>
                    <p>This customer has ${this.openSalesOrders.length} open sales order(s). You can select one to populate the invoice items.</p>
                    <div class="mb-3">
                        <label class="form-label">Select Sales Order:</label>
                        <select id="salesOrderSelect" class="form-select" onchange="handler.onSalesOrderSelected(this.value)">
                            <option value="">-- Select a Sales Order --</option>
                            ${this.openSalesOrders.map(so => `
                                <option value="${so.Id}">${so.SalesOrderId} - ${so.SalesOrderDate} (${so.SalesOrderLines?.$values.length || 0} items)</option>
                            `).join('')}
                        </select>
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="handler.populateFromSalesOrder()">
                        <i class="bi bi-arrow-down-circle"></i> Populate Invoice Items
                    </button>
                </div>
            </div>
        `;
    }

    hideSalesOrderSelection() {
        const salesOrderContainer = document.getElementById('salesOrderContainer');
        if (salesOrderContainer) {
            salesOrderContainer.remove();
        }
    }

    async onSalesOrderSelected(salesOrderId) {
        if (!salesOrderId) return;
        
        try {
            const response = await fetch(`/Invoice/GetOpenSalesOrderLines?salesOrderId=${salesOrderId}`);
            const result = await response.json();
            
            if (result.success && result.data) {
                this.selectedSalesOrderLines = result.data;
            }
        } catch (error) {
            console.error('Error loading sales order lines:', error);
        }
    }

    populateFromSalesOrder() {
        if (!this.selectedSalesOrderLines || !this.selectedSalesOrderLines.$values || this.selectedSalesOrderLines.$values.length === 0) {
            alert('Please select a sales order first.');
            return;
        }

        // Clear existing inventory items
        const inventoryContainer = document.querySelector('[data-inventory-container]');
        if (inventoryContainer) {
            inventoryContainer.innerHTML = '';
        }

        // Add inventory items from sales order lines
        let addedCount = 0;
        let duplicateCount = 0;
        
        this.selectedSalesOrderLines.$values.forEach((line, index) => {
            const success = this.addInventoryItemFromSalesOrderLine(line, index);
            if (success) {
                addedCount++;
            } else {
                duplicateCount++;
            }
        });

        // Hide the sales order selection after populating
        this.hideSalesOrderSelection();
        
        let message = `Successfully added ${addedCount} items from the selected sales order.`;
        if (duplicateCount > 0) {
            message += ` ${duplicateCount} items were skipped because they were already added.`;
        }
        alert(message);
    }

    addInventoryItemFromSalesOrderLine(line, index) {
        // This method should be called by the inventory handler
        if (window.inventoryHandler) {
            return window.inventoryHandler.addInventoryItemFromSalesOrderLine(line, index);
        }
        return false;
    }
}
