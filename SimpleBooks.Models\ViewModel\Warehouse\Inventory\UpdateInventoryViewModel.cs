﻿namespace SimpleBooks.Models.ViewModel.Warehouse.Inventory
{
    public class UpdateInventoryViewModel : BaseUpdateViewModel, IEntityMapper<InventoryModel, UpdateInventoryViewModel>
    {
        [CustomRequired]
        [DisplayName("Quantity")]
        public decimal Quantity { get; set; }
        [DisplayName("Cost Price")]
        public decimal CostPrice { get; set; }
        [DisplayName("Cost Amount")]
        public decimal CostAmount { get; set; }
        [DisplayName("Sales Price")]
        public decimal SalesPrice { get; set; }
        [DisplayName("Sales Amount")]
        public decimal SalesAmount { get; set; }
        [CustomRequired]
        [DisplayName("Unit Qty Ratio")]
        public decimal UnitQtyRatio { get; set; }
        [DisplayName("Tax Amount")]
        public decimal TaxAmount { get; set; }
        [CustomRequired]
        [DisplayName("Net Amount")]
        public decimal NetAmount { get; set; }

        [CustomRequired]
        [DisplayName("Product")]
        public Ulid ProductId { get; set; }
        [CustomRequired]
        [DisplayName("Product Unit")]
        public Ulid ProductUnitId { get; set; }
        [CustomRequired]
        [DisplayName("Store")]
        public Ulid StoreId { get; set; }
        [CustomRequired]
        [DisplayName("Transaction Type")]
        public Ulid TransactionTypeId { get; set; }
        [CustomRequired]
        [DisplayName("Transaction")]
        public Ulid TransactionId { get; set; }
        [DisplayName("Linked Transaction")]
        public Ulid? LinkedTransactionId { get; set; }
        [DisplayName("Linked Transaction Line")]
        public Ulid? LinkedTransactionLineId { get; set; }

        [DisplayName("Inventory Taxes")]
        public IList<UpdateInventoryTaxViewModel> InventoryTaxes { get; set; } = new List<UpdateInventoryTaxViewModel>();
        public string? InventoryTaxesJson { get; set; }

        public UpdateInventoryViewModel ToDto(InventoryModel entity) => entity.ToUpdateDto();

        public InventoryModel ToEntity() => InventoryMapper.ToEntity(this);
    }
}
