﻿namespace SimpleBooks.Services.Core.Business.Treasury.BankManagement.CheckManagement
{
    public interface ICheckTreasuryVoucherService : ISimpleBooksBaseService<CheckTreasuryVoucherModel, CheckTreasuryVoucherModel, CreateCheckTreasuryVoucherViewModel, UpdateCheckTreasuryVoucherViewModel>, ITreasuryVoucherService
    {
        Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveCheckVaultListAsync();
        Task<ServiceResult<IEnumerable<CheckVaultLocationModel>>> SelectiveCheckVaultLocationListAsync();
        Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveBankListAsync();
        Task<ServiceResult<IEnumerable<BankAccountModel>>> SelectiveBankAccountListAsync();

        Task<ServiceResult<IEnumerable<TreasuryVoucherDto>>> SelectiveCheckTreasuryVoucherDtoListAsync();
        Task<ServiceResult<IEnumerable<CheckTreasuryVoucherModel>>> SelectiveCheckTreasuryVouchersAsync(Ulid checkStatusId);
        Task<ServiceResult<IEnumerable<CheckTreasuryVoucherModel>>> SelectiveDepositCheckTreasuryVouchersAsync();
    }
}
