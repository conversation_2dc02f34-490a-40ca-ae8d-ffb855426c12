﻿namespace SimpleBooks.Services.Core.Business.Purchases
{
    public interface IPurchaseOrderService : ISimpleBooksBaseService<PurchaseOrderModel, IndexPurchaseOrderViewModel, CreatePurchaseOrderViewModel, UpdatePurchaseOrderViewModel>
    {
        Task<ServiceResult<IEnumerable<VendorDto>>> SelectiveVendorListAsync();
        Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveVendorTypeListAsync();
        Task<ServiceResult<IEnumerable<PaymentTermDto>>> SelectivePaymentTermListAsync();
        Task<ServiceResult<IEnumerable<ProductModel>>> SelectiveProductListAsync();
        Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveUnitListAsync();
    }
}
