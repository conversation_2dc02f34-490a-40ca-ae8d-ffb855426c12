﻿@{
    IEnumerable<CreateInventoryViewModel> casted = System.Linq.Enumerable.Cast<CreateInventoryViewModel>(Model.Inventories);
    List<CreateInventoryViewModel> inventories = casted.ToList();

    TransactionTypeEnumeration transactionType = TransactionTypeEnumeration.FromName(ViewData["TransactionType"]?.ToString() ?? string.Empty);

    IEnumerable<TransactionTypeEnumeration> transactionTypes = Enumerable.Empty<TransactionTypeEnumeration>();
    if (ViewData["TransactionTypes"]?.ToString() is string value && !string.IsNullOrWhiteSpace(value))
    {
        transactionTypes =
        JsonUtilities.ReadFromJsonString<IEnumerable<TransactionTypeEnumeration>>(value) ??
        Enumerable.Empty<TransactionTypeEnumeration>();
    }
}

<div class="">
    <h4 class="mt-4">Items</h4>
    <div class="form-group">
        <table id="InventoriesDataGridView" class="table">
            <thead>
                <tr>
                    <th>Product</th>
                    <th>Unit</th>
                    <th>Quantity</th>
                    <th>Store</th>
                    <th>Price</th>
                    <th>Amount</th>
                    <th>Taxes</th>
                    <th>Net Taxes</th>
                    <th></th>
                </tr>
            </thead>
            <tbody>
                @for (int i = 0; i < inventories.Count; i++)
                {
                    <tr data-row-index="@i">
                        <td>
                            <input type="hidden" asp-for="@inventories[i].UnitQtyRatio" name="Inventories[@i].UnitQtyRatio" />
                            <input type="hidden" asp-for="@inventories[i].TransactionTypeId" name="Inventories[@i].TransactionTypeId" value="@transactionType.Value" />
                            <input type="hidden" asp-for="@inventories[i].LinkedTransactionId" name="Inventories[@i].LinkedTransactionId" value="@transactionType.Value" />
                            <input type="hidden" asp-for="@inventories[i].LinkedTransactionLineId" name="Inventories[@i].LinkedTransactionLineId" value="@transactionType.Value" />
                            <select class="form-select" asp-for="@inventories[i].ProductId" name="Inventories[@i].ProductId" class="form-control"
                            data-placeholder="Select a product" data-minimum-results-for-search="Infinity" onchange="inventoryHandler.onProductChanged(this.value,@i)">
                                <option value=""></option>
                                @foreach (var item in Model.Products)
                                {
                                    <option value="@item.Id">@item.ProductPurchasesDescription</option>
                                }
                            </select>
                            <span asp-validation-for="@inventories[i].ProductId" class="text-danger"></span>
                        </td>
                        <td>
                            <select class="form-select" asp-for="@inventories[i].ProductUnitId" asp-items="Model.SelectiveProductUnits" name="Inventories[@i].ProductUnitId" class="form-control"
                            data-placeholder="Select a unit" data-minimum-results-for-search="Infinity">
                                <option value=""></option>
                            </select>
                            <span asp-validation-for="@inventories[i].ProductUnitId" class="text-danger"></span>
                        </td>
                        <td>
                            <input type="number" asp-for="@inventories[i].Quantity" name="Inventories[@i].Quantity" class="form-control" onchange="inventoryHandler.onQuantityChanged(this)" />
                            <span asp-validation-for="@inventories[i].Quantity" class="text-danger"></span>
                        </td>
                        <td>
                            <select class="form-select" asp-for="@inventories[i].StoreId" asp-items="Model.SelectiveStores" name="Inventories[@i].StoreId" class="form-control"
                            data-placeholder="Select a store" data-minimum-results-for-search="Infinity">
                                <option value=""></option>
                            </select>
                            <span asp-validation-for="@inventories[i].StoreId" class="text-danger"></span>
                        </td>
                        <td>
                            @if (transactionType == TransactionTypeEnumeration.Bill || transactionType == TransactionTypeEnumeration.BillReturn)
							{
                                <input type="number" asp-for="@inventories[i].CostPrice" name="Inventories[@i].CostPrice" class="form-control" onchange="inventoryHandler.onQuantityChanged(this)" />
                                <span asp-validation-for="@inventories[i].CostPrice" class="text-danger"></span>
                            }
                            else if (transactionType == TransactionTypeEnumeration.Invoice || transactionType == TransactionTypeEnumeration.InvoiceReturn)
                            {
                                <input type="number" asp-for="@inventories[i].SalesPrice" name="Inventories[@i].SalesPrice" class="form-control" onchange="inventoryHandler.onQuantityChanged(this)" />
                                <span asp-validation-for="@inventories[i].SalesPrice" class="text-danger"></span>
                            }
                            else
                            {
                            }
                        </td>
                        <td>
                            @if (transactionType == TransactionTypeEnumeration.Bill || transactionType == TransactionTypeEnumeration.BillReturn)
                            {
                                <input type="number" asp-for="@inventories[i].CostAmount" name="Inventories[@i].CostAmount" class="form-control" readonly />
                                <span asp-validation-for="@inventories[i].CostAmount" class="text-danger"></span>
                            }
                            else if (transactionType == TransactionTypeEnumeration.Invoice || transactionType == TransactionTypeEnumeration.InvoiceReturn)
                            {
                                <input type="number" asp-for="@inventories[i].SalesAmount" name="Inventories[@i].SalesAmount" class="form-control" readonly />
                                <span asp-validation-for="@inventories[i].SalesAmount" class="text-danger"></span>
                            }
                            else
                            {
                            }
                        </td>
                        <td>
                            <div class="input-group">
                                <input type="number" asp-for="@inventories[i].TaxAmount" name="Inventories[@i].TaxAmount" class="form-control" readonly />
                                <input type="hidden" asp-for="@inventories[i].InventoryTaxesJson" name="Inventories[@i].InventoryTaxesJson" class="form-control" />
                                <button type="button" asp-for="@Model.Inventories[i].InventoryTaxes" name="Inventories[@i].InventoryTaxes" class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#taxModal" onclick="inventoryHandler.onOpenSelectTaxType(@i)">Select Taxes</button>
                                @for (int ii = 0; ii < inventories[i].InventoryTaxes.Count; ii++)
                                {
                                    <input type="hidden" asp-for="@inventories[i].InventoryTaxes[ii].TaxTypeId" name="Inventories[@i].InventoryTaxes[@ii].TaxTypeId" class="form-control" />
                                    <input type="hidden" asp-for="@inventories[i].InventoryTaxes[ii].TaxSubTypeId" name="Inventories[@i].InventoryTaxes[@ii].TaxSubTypeId" class="form-control" />
                                    <input type="hidden" asp-for="@inventories[i].InventoryTaxes[ii].InventoryTaxsRatio" name="Inventories[@i].InventoryTaxes[@ii].InventoryTaxsRatio" class="form-control" />
                                    <input type="hidden" asp-for="@inventories[i].InventoryTaxes[ii].InventoryTaxsAmount" name="Inventories[@i].InventoryTaxes[@ii].InventoryTaxsAmount" class="form-control" />
                                }
                            </div>
                            <span asp-validation-for="@inventories[i].TaxAmount" class="text-danger"></span>
                        </td>
                        <td>
                            <input type="number" asp-for="@inventories[i].NetAmount" name="Inventories[@i].NetAmount" class="form-control" readonly />
                            <span asp-validation-for="@inventories[i].NetAmount" class="text-danger"></span>
                        </td>
                        <td>
                            <button type="button" class="btn btn-danger" onclick="removeRow(this)">Remove</button>
                        </td>
                    </tr>
                }
            </tbody>
        </table>
        <button type="button" class="btn btn-primary" onclick="inventoryHandler.addProductRow('@transactionType.Value')">Add Row</button>
    </div>
</div>

<!-- taxModal -->
<div class="modal fade" id="taxModal" tabindex="-1" aria-labelledby="taxModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Select Taxes</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" onclick="inventoryHandler.closeTaxSelection()"></button>
            </div>
            <div class="modal-body">
                <table id="InventoryTaxesDataGridView" class="table">
                    <thead>
                        <tr>
                            <th>Tax Type</th>
                            <th>Tax Sub-Type</th>
                            <th>Ratio</th>
                            <th>Amount</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
                <button type="button" class="btn btn-primary" onclick="inventoryHandler.addTaxRow()">Add Row</button>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-success" onclick="inventoryHandler.confirmTaxSelection()">Confirm</button>
            </div>
        </div>
    </div>
</div>
