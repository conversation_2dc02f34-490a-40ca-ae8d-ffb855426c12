﻿@model CreatePurchaseOrderFormViewModel

@{
    ViewData["Title"] = "Add Purchase Order";
}

<h5>
    <i class="bi bi-plus-circle-dotted"></i>
    Add a new Purchase Order
</h5>

<form asp-controller="PurchaseOrder" enctype="multipart/form-data">
    <div asp-validation-summary="All" class="text-danger" data-validation-summary="true"></div>
    <div class="row">
        <div class="col-md-6 mt-2">
            <div class="form-group">
                <label asp-for="PurchaseOrderId" class="form-label mt-2"></label>
                <input type="text" class="form-control" asp-for="PurchaseOrderId" placeholder="PurchaseOrder Id">
                <span asp-validation-for="PurchaseOrderId" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="PurchaseOrderDate" class="form-label mt-2"></label>
                <input type="date" class="form-control" asp-for="PurchaseOrderDate" placeholder="PurchaseOrder Date" value="@DateTime.Now.ToString("yyyy-MM-dd")">
                <span asp-validation-for="PurchaseOrderDate" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="VendorId" class="form-label mt-2"></label>
                <select class="form-select" asp-for="VendorId"
                data-placeholder="Select a vendor" data-minimum-results-for-search="Infinity" onchange="handler.onVendorChanged(this.value)">
                    <option value=""></option>
                    @foreach (var item in Model.Vendors)
                    {
                        <option value="@item.Id">@item.VendorName</option>
                    }
                </select>
                <span asp-validation-for="VendorId" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="VendorTypeId" class="form-label mt-2"></label>
                <select class="form-select" asp-for="VendorTypeId" asp-items="Model.VendorTypes"
                data-placeholder="Select a vendor type" data-minimum-results-for-search="Infinity">
                    <option value=""></option>
                </select>
                <span asp-validation-for="VendorTypeId" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="PaymentTermId" class="form-label mt-2"></label>
                <select class="form-select" asp-for="PaymentTermId"
                data-placeholder="Select a payment term" data-minimum-results-for-search="Infinity" onchange="handler.onPaymentTermChanged(this.value)">
                    <option value=""></option>
                    @foreach (var item in Model.PaymentTerms)
                    {
                        <option value="@item.Id">@item.PaymentTermName</option>
                    }
                </select>
                <span asp-validation-for="PaymentTermId" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="PurchaseOrderDueDate" class="form-label mt-2"></label>
                <input type="date" class="form-control" asp-for="PurchaseOrderDueDate" placeholder="PurchaseOrder Due Date" value="@DateTime.Now.ToString("yyyy-MM-dd")">
                <span asp-validation-for="PurchaseOrderDueDate" class="text-danger"></span>
            </div>
        </div>
        <div class="row">
            <h4 class="mt-4">Items</h4>
            <div class="form-group">
                <table id="PurchaseOrderLinesDataGridView" class="table">
                    <thead>
                        <tr>
                            <th>Product</th>
                            <th>Unit</th>
                            <th>Quantity</th>
                            <th>Price</th>
                            <th>Amount</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        @for (int i = 0; i < Model.PurchaseOrderLines.Count; i++)
                        {
                            <tr data-row-index="@i">
                                <td>
                                    <input type="hidden" asp-for="@Model.PurchaseOrderLines[i].UnitQtyRatio" name="PurchaseOrderLines[@i].UnitQtyRatio" />
                                    <select class="form-select" asp-for="@Model.PurchaseOrderLines[i].ProductId" name="PurchaseOrderLines[@i].ProductId" class="form-control"
                                            data-placeholder="Select a product" data-minimum-results-for-search="Infinity" onchange="handler.onProductChanged(this.value,@i)">
                                        <option value=""></option>
                                        @foreach (var item in Model.Products)
                                        {
                                            <option value="@item.Id">@item.ProductPurchasesDescription</option>
                                        }
                                    </select>
                                    <span asp-validation-for="@Model.PurchaseOrderLines[i].ProductId" class="text-danger"></span>
                                </td>
                                <td>
                                    <select class="form-select" asp-for="@Model.PurchaseOrderLines[i].ProductUnitId" asp-items="Model.SelectiveProductUnits" name="PurchaseOrderLines[@i].ProductUnitId" class="form-control"
                                            data-placeholder="Select a unit" data-minimum-results-for-search="Infinity">
                                        <option value=""></option>
                                    </select>
                                    <span asp-validation-for="@Model.PurchaseOrderLines[i].ProductUnitId" class="text-danger"></span>
                                </td>
                                <td>
                                    <input type="number" asp-for="@Model.PurchaseOrderLines[i].Quantity" name="PurchaseOrderLines[@i].Quantity" class="form-control" onchange="handler.onQuantityChanged(this)" />
                                    <span asp-validation-for="@Model.PurchaseOrderLines[i].Quantity" class="text-danger"></span>
                                </td>
                                <td>
                                    <input type="number" asp-for="@Model.PurchaseOrderLines[i].Price" name="PurchaseOrderLines[@i].Price" class="form-control" onchange="handler.onQuantityChanged(this)" />
                                    <span asp-validation-for="@Model.PurchaseOrderLines[i].Price" class="text-danger"></span>
                                </td>
                                <td>
                                    <input type="number" asp-for="@Model.PurchaseOrderLines[i].Amount" name="PurchaseOrderLines[@i].Amount" class="form-control" readonly />
                                    <span asp-validation-for="@Model.PurchaseOrderLines[i].Amount" class="text-danger"></span>
                                </td>
                                <td>
                                    <button type="button" class="btn btn-danger" onclick="removeRow(this)">Remove</button>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
                <button type="button" class="btn btn-primary" onclick="handler.addProductRow()">Add Row</button>
            </div>
        </div>
        <div class="col-md-6 mt-2">
            <button type="submit" class="btn btn-primary mt-4">Save</button>
        </div>
    </div>
</form>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
	<script src="~/js/tools.js"></script>
    <script type="module">
        import { PurchaseOrderHandler } from '/js/business/Purchases/purchaseOrderHandler.js';
        const vendors = @Html.Raw(Json.Serialize(Model.Vendors));
        const paymentTerms = @Html.Raw(Json.Serialize(Model.PaymentTerms));
        const products = @Html.Raw(Json.Serialize(Model.Products));

        window.handler = new PurchaseOrderHandler(
            vendors,
            paymentTerms,
            products
        );
    </script>
}
