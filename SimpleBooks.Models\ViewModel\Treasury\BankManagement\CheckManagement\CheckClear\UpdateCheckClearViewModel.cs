﻿namespace SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckClear
{
    public class UpdateCheckClearViewModel : BaseUpdateViewModel, IEntityMapper<CheckClearModel, UpdateCheckClearViewModel>
    {
        [CustomRequired]
        [DisplayName("Clear Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateTime ClearDate { get; set; }

        [CustomRequired]
        [DisplayName("Check Treasury Vouchers")]
        public virtual ICollection<Ulid> SelectedCheckTreasuryVouchers { get; set; } = new List<Ulid>();

        public UpdateCheckClearViewModel ToDto(CheckClearModel entity) => entity.ToUpdateDto();

        public CheckClearModel ToEntity() => CheckClearMapper.ToEntity(this);
    }
}
