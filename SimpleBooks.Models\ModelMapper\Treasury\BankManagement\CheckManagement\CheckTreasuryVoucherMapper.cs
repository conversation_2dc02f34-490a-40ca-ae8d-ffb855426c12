﻿namespace SimpleBooks.Models.ModelMapper.Treasury.BankManagement.CheckManagement
{
    public static class CheckTreasuryVoucherMapper
    {
        public static CreateCheckTreasuryVoucherViewModel ToCreateDto(this CheckTreasuryVoucherModel entity)
        {
            CreateCheckTreasuryVoucherViewModel viewModel = new CreateCheckTreasuryVoucherViewModel()
            {
                TransactionDate = entity.TransactionDate,
                Amount = entity.Amount,
                Note = entity.Note,
                RefranceNumber = entity.RefranceNumber,
                TransactionTypeId = entity.TransactionTypeId,
                BeneficiaryTypeId = entity.BeneficiaryTypeId,
                BeneficiaryId = entity.GetBeneficiaryId(),
                CheckNumber = entity.CheckNumber,
                DueDate = entity.DueDate,
                IssuerName = entity.IssuerName,
                BearerName = entity.BearerName,
                CheckStatusId = entity.CheckStatusId,
                CheckVaultId = entity.CheckVaultId,
                CheckVaultLocationId = entity.CheckVaultLocationId,
                BankId = entity.BankId,
                BankAccountId = entity.BankAccountId,
                TreasuryLines = entity.TreasuryLines.Select(x => x.ToCreateDto()).ToList(),
                CheckStatusHistories = entity.CheckStatusHistories.Select(x => x.ToCreateDto()).ToList(),
            };
            return viewModel;
        }

        public static CheckTreasuryVoucherModel ToEntity(this CreateCheckTreasuryVoucherViewModel entity)
        {
            CheckTreasuryVoucherModel model = new CheckTreasuryVoucherModel()
            {
                TransactionDate = entity.TransactionDate,
                Amount = entity.Amount,
                Note = entity.Note,
                RefranceNumber = entity.RefranceNumber,
                TransactionTypeId = entity.TransactionTypeId,
                BeneficiaryTypeId = entity.BeneficiaryTypeId,
                VendorId = entity.BeneficiaryTypeId == BeneficiaryTypeEnumeration.Vendor.Value ? entity.BeneficiaryId : null,
                CustomerId = entity.BeneficiaryTypeId == BeneficiaryTypeEnumeration.Customer.Value ? entity.BeneficiaryId : null,
                EmployeeId = entity.BeneficiaryTypeId == BeneficiaryTypeEnumeration.Employee.Value ? entity.BeneficiaryId : null,
                CheckNumber = entity.CheckNumber,
                DueDate = entity.DueDate,
                IssuerName = entity.IssuerName,
                BearerName = entity.BearerName,
                CheckStatusId = entity.CheckStatusId,
                CheckVaultId = entity.CheckVaultId,
                CheckVaultLocationId = entity.CheckVaultLocationId,
                BankId = entity.BankId,
                BankAccountId = entity.BankAccountId,
                TreasuryLines = entity.TreasuryLines.Select(x => x.ToEntity()).ToList(),
                CheckStatusHistories = entity.CheckStatusHistories.Select(x => x.ToEntity()).ToList(),
            };
            return model;
        }

        public static UpdateCheckTreasuryVoucherViewModel ToUpdateDto(this CheckTreasuryVoucherModel entity)
        {
            UpdateCheckTreasuryVoucherViewModel viewModel = new UpdateCheckTreasuryVoucherViewModel()
            {
                Id = entity.Id,
                TransactionDate = entity.TransactionDate,
                VoucherSerial = entity.VoucherSerial,
                TVID = entity.TVID,
                Amount = entity.Amount,
                Note = entity.Note,
                RefranceNumber = entity.RefranceNumber,
                TransactionTypeId = entity.TransactionTypeId,
                BeneficiaryTypeId = entity.BeneficiaryTypeId,
                BeneficiaryId = entity.GetBeneficiaryId(),
                CheckNumber = entity.CheckNumber,
                DueDate = entity.DueDate,
                IssuerName = entity.IssuerName,
                BearerName = entity.BearerName,
                CheckStatusId = entity.CheckStatusId,
                CheckVaultId = entity.CheckVaultId,
                CheckVaultLocationId = entity.CheckVaultLocationId,
                BankId = entity.BankId,
                BankAccountId = entity.BankAccountId,
                TreasuryLines = entity.TreasuryLines.Select(x => x.ToUpdateDto()).ToList(),
                CheckStatusHistories = entity.CheckStatusHistories.Select(x => x.ToUpdateDto()).ToList(),
            };
            return viewModel;
        }

        public static CheckTreasuryVoucherModel ToEntity(this UpdateCheckTreasuryVoucherViewModel entity)
        {
            CheckTreasuryVoucherModel model = new CheckTreasuryVoucherModel()
            {
                Id = entity.Id,
                TransactionDate = entity.TransactionDate,
                VoucherSerial = entity.VoucherSerial,
                TVID = entity.TVID,
                Amount = entity.Amount,
                Note = entity.Note,
                RefranceNumber = entity.RefranceNumber,
                TransactionTypeId = entity.TransactionTypeId,
                BeneficiaryTypeId = entity.BeneficiaryTypeId,
                VendorId = entity.BeneficiaryTypeId == BeneficiaryTypeEnumeration.Vendor.Value ? entity.BeneficiaryId : null,
                CustomerId = entity.BeneficiaryTypeId == BeneficiaryTypeEnumeration.Customer.Value ? entity.BeneficiaryId : null,
                EmployeeId = entity.BeneficiaryTypeId == BeneficiaryTypeEnumeration.Employee.Value ? entity.BeneficiaryId : null,
                CheckNumber = entity.CheckNumber,
                DueDate = entity.DueDate,
                IssuerName = entity.IssuerName,
                BearerName = entity.BearerName,
                CheckStatusId = entity.CheckStatusId,
                CheckVaultId = entity.CheckVaultId,
                CheckVaultLocationId = entity.CheckVaultLocationId,
                BankId = entity.BankId,
                BankAccountId = entity.BankAccountId,
                TreasuryLines = entity.TreasuryLines.Select(x => x.ToEntity()).ToList(),
                CheckStatusHistories = entity.CheckStatusHistories.Select(x => x.ToEntity()).ToList(),
            };
            return model;
        }

        public static Ulid GetBeneficiaryId(this CheckTreasuryVoucherModel entity)
        {
            if (entity.VendorId.HasValue)
                return entity.VendorId.Value;
            else if (entity.CustomerId.HasValue)
                return entity.CustomerId.Value;
            else if (entity.EmployeeId.HasValue)
                return entity.EmployeeId.Value;
            throw new InvalidOperationException("No beneficiary ID found.");
        }
    }
}
