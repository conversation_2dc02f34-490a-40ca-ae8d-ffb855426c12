﻿namespace SimpleBooks.Models.Model.Sales
{
    [Table("Customer")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<CustomerModel>))]
    public class CustomerModel : BaseWithTrackingModel
    {
        [CustomRequired]
        [DisplayName("Customer Name")]
        public string CustomerName { get; set; }
        [CustomRequired]
        [StringLength(9)]
        [DisplayName("Customer Tax Card Number")]
        public string CustomerTaxCardNumber { get; set; }

        [DisplayName("Customer Type")]
        public Ulid? CustomerTypeId { get; set; }
        public virtual CustomerTypeModel? CustomerType { get; set; }

        [DisplayName("Customer Sales Rep")]
        public Ulid? CustomerRepId { get; set; }
        public virtual EmployeeModel? CustomerRep { get; set; }

        [DisplayName("Customer Payment Term")]
        public Ulid? PaymentTermId { get; set; }
        public virtual PaymentTermModel? PaymentTerm { get; set; }

        public virtual ICollection<InvoiceModel> Invoices { get; set; } = new List<InvoiceModel>();
        public virtual ICollection<InvoiceReturnModel> InvoiceReturns { get; set; } = new List<InvoiceReturnModel>();
        public virtual ICollection<SalesOrderModel> SalesOrders { get; set; } = new List<SalesOrderModel>();
        [DisplayName("Cash Treasury Vouchers")]
        public virtual ICollection<CashTreasuryVoucherModel> CashTreasuryVouchers { get; set; } = new List<CashTreasuryVoucherModel>();
        [DisplayName("Check Treasury Vouchers")]
        public virtual ICollection<CheckTreasuryVoucherModel> CheckTreasuryVouchers { get; set; } = new List<CheckTreasuryVoucherModel>();
        [DisplayName("Bank Transfer Treasury Vouchers")]
        public virtual ICollection<BankTransferTreasuryVoucherModel> BankTransferTreasuryVouchers { get; set; } = new List<BankTransferTreasuryVoucherModel>();
    }
}
