﻿namespace SimpleBooks.Repositories.EF.Factory.Config.Warehouse
{
    public class InventoryConfiguration : IEntityTypeConfiguration<InventoryModel>
    {
        public void Configure(EntityTypeBuilder<InventoryModel> builder)
        {
            builder.<PERSON><PERSON><PERSON>(x => new { x.Id });

            builder.HasOne(d => d.TransactionType).WithMany(p => p.Inventories)
                .HasForeignKey(d => d.TransactionTypeId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.Product).WithMany(p => p.Inventories)
                .HasForeignKey(d => d.ProductId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.ProductUnit).WithMany(p => p.Inventories)
                .HasForeignKey(d => d.ProductUnitId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.Store).WithMany(p => p.Inventories)
                .HasForeignKey(d => d.StoreId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.Bill).WithMany(p => p.Inventories)
                .HasForeignKey(d => d.BillId)
                .OnDelete(DeleteBehavior.ClientCascade)
                .IsRequired(false);

            builder.HasOne(d => d.BillReturn).WithMany(p => p.Inventories)
                .HasForeignKey(d => d.BillReturnId)
                .OnDelete(DeleteBehavior.ClientCascade)
                .IsRequired(false);

            builder.HasOne(d => d.PurchaseOrder).WithMany(p => p.Inventories)
                .HasForeignKey(d => d.PurchaseOrderId)
                .OnDelete(DeleteBehavior.ClientCascade)
                .IsRequired(false);

            builder.HasOne(d => d.PurchaseOrderLine).WithMany(p => p.Inventories)
                .HasForeignKey(d => d.PurchaseOrderLineId)
                .OnDelete(DeleteBehavior.ClientCascade)
                .IsRequired(false);

            builder.HasOne(d => d.Invoice).WithMany(p => p.Inventories)
                .HasForeignKey(d => d.InvoiceId)
                .OnDelete(DeleteBehavior.ClientCascade)
                .IsRequired(false);

            builder.HasOne(d => d.InvoiceReturn).WithMany(p => p.Inventories)
                .HasForeignKey(d => d.InvoiceReturnId)
                .OnDelete(DeleteBehavior.ClientCascade)
                .IsRequired(false);

            builder.HasOne(d => d.SalesOrder).WithMany(p => p.Inventories)
                .HasForeignKey(d => d.SalesOrderId)
                .OnDelete(DeleteBehavior.ClientCascade)
                .IsRequired(false);

            builder.HasOne(d => d.SalesOrderLine).WithMany(p => p.Inventories)
                .HasForeignKey(d => d.SalesOrderLineId)
                .OnDelete(DeleteBehavior.ClientCascade)
                .IsRequired(false);
        }
    }
}
