﻿namespace SimpleBooks.Models.Model.Warehouse
{
    [Table("Inventory")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<InventoryModel>))]
    public class InventoryModel : BaseWithoutTrackingModel
    {
        [CustomRequired]
        [DisplayName("Quantity")]
        public decimal Quantity { get; set; }
        [CustomRequired]
        [DisplayName("Cost Price")]
        public decimal CostPrice { get; set; }
        [CustomRequired]
        [DisplayName("Cost Amount")]
        public decimal CostAmount { get; set; }
        [CustomRequired]
        [DisplayName("Sales Price")]
        public decimal SalesPrice { get; set; }
        [CustomRequired]
        [DisplayName("Sales Amount")]
        public decimal SalesAmount { get; set; }
        [CustomRequired]
        [DisplayName("Unit Qty Ratio")]
        public decimal UnitQtyRatio { get; set; }
        [CustomRequired]
        [DisplayName("Tax Amount")]
        public decimal TaxAmount { get; set; }
        [CustomRequired]
        [DisplayName("Net Amount")]
        public decimal NetAmount { get; set; }

        [CustomRequired]
        [DisplayName("Transaction Type")]
        public Ulid TransactionTypeId { get; set; }
        public virtual TransactionTypeModel? TransactionType { get; set; }

        [CustomRequired]
        [DisplayName("Product")]
        public Ulid ProductId { get; set; }
        public virtual ProductModel? Product { get; set; }

        [CustomRequired]
        [DisplayName("Product Unit")]
        public Ulid ProductUnitId { get; set; }
        public virtual UnitModel? ProductUnit { get; set; }

        [CustomRequired]
        [DisplayName("Store")]
        public Ulid StoreId { get; set; }
        public virtual StoreModel? Store { get; set; }

        [DisplayName("Bill")]
        public Ulid? BillId { get; set; }
        public virtual BillModel? Bill { get; set; }

        [DisplayName("BillReturn")]
        public Ulid? BillReturnId { get; set; }
        public virtual BillReturnModel? BillReturn { get; set; }

        [DisplayName("PurchaseOrder")]
        public Ulid? PurchaseOrderId { get; set; }
        public virtual PurchaseOrderModel? PurchaseOrder { get; set; }

        [DisplayName("PurchaseOrderLine")]
        public Ulid? PurchaseOrderLineId { get; set; }
        public virtual PurchaseOrderLineModel? PurchaseOrderLine { get; set; }

        [DisplayName("Invoice")]
        public Ulid? InvoiceId { get; set; }
        public virtual InvoiceModel? Invoice { get; set; }

        [DisplayName("InvoiceReturn")]
        public Ulid? InvoiceReturnId { get; set; }
        public virtual InvoiceReturnModel? InvoiceReturn { get; set; }

        [DisplayName("SalesOrder")]
        public Ulid? SalesOrderId { get; set; }
        public virtual SalesOrderModel? SalesOrder { get; set; }

        [DisplayName("SalesOrderLine")]
        public Ulid? SalesOrderLineId { get; set; }
        public virtual SalesOrderLineModel? SalesOrderLine { get; set; }

        [DisplayName("Inventory Taxes")]
        public virtual ICollection<InventoryTaxModel> InventoryTaxes { get; set; } = new List<InventoryTaxModel>();
    }
}
