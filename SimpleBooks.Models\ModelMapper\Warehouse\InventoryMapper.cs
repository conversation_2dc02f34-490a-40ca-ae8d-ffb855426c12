﻿namespace SimpleBooks.Models.ModelMapper.Warehouse
{
    public static class InventoryMapper
    {
        public static CreateInventoryViewModel ToCreateDto(this InventoryModel entity)
        {
            CreateInventoryViewModel viewModel = new CreateInventoryViewModel()
            {
                Quantity = entity.Quantity,
                CostPrice = entity.CostPrice,
                CostAmount = entity.CostAmount,
                SalesPrice = entity.SalesPrice,
                SalesAmount = entity.SalesAmount,
                UnitQtyRatio = entity.UnitQtyRatio,
                TaxAmount = entity.TaxAmount,
                NetAmount = entity.NetAmount,
                ProductId = entity.ProductId,
                ProductUnitId = entity.ProductUnitId,
                StoreId = entity.StoreId,
                TransactionTypeId = entity.TransactionTypeId,
                TransactionId = GetTransactionId(entity),
                LinkedTransactionId = GetLinkedTransactionId(entity),
                LinkedTransactionLineId = GetLinkedTransactionLineId(entity),
                InventoryTaxes = entity.InventoryTaxes.Select(x => x.ToCreateDto()).ToList(),
            };
            return viewModel;
        }

        public static InventoryModel ToEntity(this CreateInventoryViewModel entity)
        {
            InventoryModel model = new InventoryModel()
            {
                Quantity = entity.Quantity,
                CostPrice = entity.CostPrice,
                CostAmount = entity.CostAmount,
                SalesPrice = entity.SalesPrice,
                SalesAmount = entity.SalesAmount,
                UnitQtyRatio = entity.UnitQtyRatio,
                TaxAmount = entity.TaxAmount,
                NetAmount = entity.NetAmount,
                ProductId = entity.ProductId,
                ProductUnitId = entity.ProductUnitId,
                StoreId = entity.StoreId,
                TransactionTypeId = entity.TransactionTypeId,
                BillId = entity.TransactionTypeId == TransactionTypeEnumeration.Bill.Value ? entity.TransactionId : null,
                BillReturnId = entity.TransactionTypeId == TransactionTypeEnumeration.BillReturn.Value ? entity.TransactionId : null,
                PurchaseOrderId = (entity.TransactionTypeId == TransactionTypeEnumeration.Bill.Value || entity.TransactionTypeId == TransactionTypeEnumeration.BillReturn.Value) ? entity.LinkedTransactionId : null,
                PurchaseOrderLineId = (entity.TransactionTypeId == TransactionTypeEnumeration.Bill.Value || entity.TransactionTypeId == TransactionTypeEnumeration.BillReturn.Value) ? entity.LinkedTransactionLineId : null,
                InvoiceId = entity.TransactionTypeId == TransactionTypeEnumeration.Invoice.Value ? entity.TransactionId : null,
                InvoiceReturnId = entity.TransactionTypeId == TransactionTypeEnumeration.InvoiceReturn.Value ? entity.TransactionId : null,
                SalesOrderId = (entity.TransactionTypeId == TransactionTypeEnumeration.Invoice.Value || entity.TransactionTypeId == TransactionTypeEnumeration.InvoiceReturn.Value) ? entity.LinkedTransactionId : null,
                SalesOrderLineId = (entity.TransactionTypeId == TransactionTypeEnumeration.Invoice.Value || entity.TransactionTypeId == TransactionTypeEnumeration.InvoiceReturn.Value) ? entity.LinkedTransactionLineId : null,
                InventoryTaxes = entity.InventoryTaxes.Select(x => x.ToEntity()).ToList(), 
            };
            return model;
        }

        public static UpdateInventoryViewModel ToUpdateDto(this InventoryModel entity)
        {
            UpdateInventoryViewModel viewModel = new UpdateInventoryViewModel()
            {
                Id = entity.Id,
                Quantity = entity.Quantity,
                CostPrice = entity.CostPrice,
                CostAmount = entity.CostAmount,
                SalesPrice = entity.SalesPrice,
                SalesAmount = entity.SalesAmount,
                UnitQtyRatio = entity.UnitQtyRatio,
                TaxAmount = entity.TaxAmount,
                NetAmount = entity.NetAmount,
                ProductId = entity.ProductId,
                ProductUnitId = entity.ProductUnitId,
                StoreId = entity.StoreId,
                TransactionTypeId = entity.TransactionTypeId,
                TransactionId = GetTransactionId(entity),
                LinkedTransactionId = GetLinkedTransactionId(entity),
                LinkedTransactionLineId = GetLinkedTransactionLineId(entity),
                InventoryTaxes = entity.InventoryTaxes.Select(x => x.ToUpdateDto()).ToList(),
            };
            return viewModel;
        }

        public static InventoryModel ToEntity(this UpdateInventoryViewModel entity)
        {
            InventoryModel model = new InventoryModel()
            {
                Id = entity.Id,
                Quantity = entity.Quantity,
                CostPrice = entity.CostPrice,
                CostAmount = entity.CostAmount,
                SalesPrice = entity.SalesPrice,
                SalesAmount = entity.SalesAmount,
                UnitQtyRatio = entity.UnitQtyRatio,
                TaxAmount = entity.TaxAmount,
                NetAmount = entity.NetAmount,
                ProductId = entity.ProductId,
                ProductUnitId = entity.ProductUnitId,
                StoreId = entity.StoreId,
                TransactionTypeId = entity.TransactionTypeId,
                BillId = entity.TransactionTypeId == TransactionTypeEnumeration.Bill.Value ? entity.TransactionId : null,
                BillReturnId = entity.TransactionTypeId == TransactionTypeEnumeration.BillReturn.Value ? entity.TransactionId : null,
                PurchaseOrderId = (entity.TransactionTypeId == TransactionTypeEnumeration.Bill.Value || entity.TransactionTypeId == TransactionTypeEnumeration.BillReturn.Value) ? entity.LinkedTransactionId : null,
                PurchaseOrderLineId = (entity.TransactionTypeId == TransactionTypeEnumeration.Bill.Value || entity.TransactionTypeId == TransactionTypeEnumeration.BillReturn.Value) ? entity.LinkedTransactionLineId : null,
                InvoiceId = entity.TransactionTypeId == TransactionTypeEnumeration.Invoice.Value ? entity.TransactionId : null,
                InvoiceReturnId = entity.TransactionTypeId == TransactionTypeEnumeration.InvoiceReturn.Value ? entity.TransactionId : null,
                SalesOrderId = (entity.TransactionTypeId == TransactionTypeEnumeration.Invoice.Value || entity.TransactionTypeId == TransactionTypeEnumeration.InvoiceReturn.Value) ? entity.LinkedTransactionId : null,
                SalesOrderLineId = (entity.TransactionTypeId == TransactionTypeEnumeration.Invoice.Value || entity.TransactionTypeId == TransactionTypeEnumeration.InvoiceReturn.Value) ? entity.LinkedTransactionLineId : null,
                InventoryTaxes = entity.InventoryTaxes.Select(x => x.ToEntity()).ToList(),
            };
            return model;
        }

        public static Ulid GetTransactionId(this InventoryModel entity)
        {
            if (entity.BillId.HasValue)
                return entity.BillId.Value;
            else if (entity.BillReturnId.HasValue)
                return entity.BillReturnId.Value;
            else if (entity.InvoiceId.HasValue)
                return entity.InvoiceId.Value;
            else if (entity.InvoiceReturnId.HasValue)
                return entity.InvoiceReturnId.Value;
            throw new InvalidOperationException("No transaction ID found.");
        }

        public static Ulid GetLinkedTransactionId(this InventoryModel entity)
        {
            if ((entity.BillId.HasValue || entity.BillReturnId.HasValue) && entity.PurchaseOrderId.HasValue)
                return entity.PurchaseOrderId.Value;
            else if ((entity.InvoiceId.HasValue || entity.InvoiceReturnId.HasValue) && entity.SalesOrderId.HasValue)
                return entity.SalesOrderId.Value;
            throw new InvalidOperationException("No linked transaction ID found.");
        }

        public static Ulid GetLinkedTransactionLineId(this InventoryModel entity)
        {
            if ((entity.BillId.HasValue || entity.BillReturnId.HasValue) && entity.PurchaseOrderLineId.HasValue)
                return entity.PurchaseOrderLineId.Value;
            else if ((entity.InvoiceId.HasValue || entity.InvoiceReturnId.HasValue) && entity.SalesOrderLineId.HasValue)
                return entity.SalesOrderLineId.Value;
            throw new InvalidOperationException("No linked transaction line ID found.");
        }
    }
}
