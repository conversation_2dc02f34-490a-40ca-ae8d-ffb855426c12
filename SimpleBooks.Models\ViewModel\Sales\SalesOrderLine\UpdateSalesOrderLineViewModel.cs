﻿namespace SimpleBooks.Models.ViewModel.Sales.SalesOrderLine
{
    public class UpdateSalesOrderLineViewModel : BaseUpdateViewModel, IEntityMapper<SalesOrderLineModel, UpdateSalesOrderLineViewModel>
    {
        [CustomRequired]
        [DisplayName("Quantity")]
        public decimal Quantity { get; set; }
        [CustomRequired]
        [DisplayName("Price")]
        public decimal Price { get; set; }
        [CustomRequired]
        [DisplayName("Amount")]
        public decimal Amount { get; set; }
        [CustomRequired]
        [DisplayName("Unit Qty Ratio")]
        public decimal UnitQtyRatio { get; set; }

        [CustomRequired]
        [DisplayName("Product")]
        public Ulid ProductId { get; set; }
        [CustomRequired]
        [DisplayName("Product Unit")]
        public Ulid ProductUnitId { get; set; }
        [CustomRequired]
        [DisplayName("SalesOrder")]
        public Ulid SalesOrderId { get; set; }

        public UpdateSalesOrderLineViewModel ToDto(SalesOrderLineModel entity) => entity.ToUpdateDto();

        public SalesOrderLineModel ToEntity() => SalesOrderLineMapper.ToEntity(this);
    }
}
