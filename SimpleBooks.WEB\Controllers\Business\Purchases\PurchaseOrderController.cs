﻿namespace SimpleBooks.WEB.Controllers.Business.Purchases
{
    public class PurchaseOrderController : BaseBusinessController<
        PurchaseOrderModel,
        IndexPurchaseOrderViewModel,
        CreatePurchaseOrderViewModel,
        UpdatePurchaseOrderViewModel,
        IndexPurchaseOrderFormViewModel,
        CreatePurchaseOrderFormViewModel,
        UpdatePurchaseOrderFormViewModel>
    {
        private readonly IPurchaseOrderService _purchaseOrderService;

        public PurchaseOrderController(IPurchaseOrderService purchaseOrderService) : base(purchaseOrderService)
        {
            _purchaseOrderService = purchaseOrderService;
        }

        public override async Task<IActionResult> Create(CreatePurchaseOrderFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FullFillViewAsync(model);

            try
            {
                var result = await _purchaseOrderService.AddAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("purchaseOrder", ex.Message);
                return await FullFillViewAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        public override async Task<IActionResult> Update(Ulid id)
        {
            PurchaseOrderModel? entity = await _purchaseOrderService.GetByIdAsync(id);

            if (entity is null)
                return NotFound();

            UpdatePurchaseOrderFormViewModel viewModel = new UpdatePurchaseOrderFormViewModel()
            {
                Id = entity.Id,
                PurchaseOrderId = entity.PurchaseOrderId,
                PurchaseOrderDate = entity.PurchaseOrderDate,
                PurchaseOrderDueDate = entity.PurchaseOrderDueDate,
                VendorId = entity.VendorId,
                VendorTypeId = entity.VendorTypeId,
                PaymentTermId = entity.PaymentTermId,
                PurchaseOrderLines = entity.PurchaseOrderLines.Select(x => x.ToUpdateDto()).ToList(),
                Vendors = await _purchaseOrderService.SelectiveVendorListAsync().GetDataOrThrowIfNullAsync(),
                VendorTypes = await _purchaseOrderService.SelectiveVendorTypeListAsync().ToSelectListItemAsync(),
                PaymentTerms = await _purchaseOrderService.SelectivePaymentTermListAsync().GetDataOrThrowIfNullAsync(),
                Products = await _purchaseOrderService.SelectiveProductListAsync().GetDataOrThrowIfNullAsync(),
                SelectiveProductUnits = await _purchaseOrderService.SelectiveUnitListAsync().ToSelectListItemAsync(),
            };

            return View(viewModel);
        }

        public override async Task<IActionResult> Update(UpdatePurchaseOrderFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FailedActionResultAsync(model);

            try
            {
                var result = await _purchaseOrderService.UpdateAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("purchaseOrder", ex.Message);
                return await FailedActionResultAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        protected override async Task<IActionResult> FullFillViewAsync(CreatePurchaseOrderFormViewModel model)
        {

            model.Vendors = await _purchaseOrderService.SelectiveVendorListAsync().GetDataOrThrowIfNullAsync();
            model.VendorTypes = await _purchaseOrderService.SelectiveVendorTypeListAsync().ToSelectListItemAsync();
            model.PaymentTerms = await _purchaseOrderService.SelectivePaymentTermListAsync().GetDataOrThrowIfNullAsync();
            model.Products = await _purchaseOrderService.SelectiveProductListAsync().GetDataOrThrowIfNullAsync();
            model.SelectiveProductUnits = await _purchaseOrderService.SelectiveUnitListAsync().ToSelectListItemAsync();
            return await Task.FromResult(View(model));
        }

        protected override async Task<IActionResult> FailedActionResultAsync(UpdatePurchaseOrderFormViewModel model)
        {
            model.Vendors = await _purchaseOrderService.SelectiveVendorListAsync().GetDataOrThrowIfNullAsync();
            model.VendorTypes = await _purchaseOrderService.SelectiveVendorTypeListAsync().ToSelectListItemAsync();
            model.PaymentTerms = await _purchaseOrderService.SelectivePaymentTermListAsync().GetDataOrThrowIfNullAsync();
            model.Products = await _purchaseOrderService.SelectiveProductListAsync().GetDataOrThrowIfNullAsync();
            model.SelectiveProductUnits = await _purchaseOrderService.SelectiveUnitListAsync().ToSelectListItemAsync();
            return await Task.FromResult(View(model));
        }
    }
}
