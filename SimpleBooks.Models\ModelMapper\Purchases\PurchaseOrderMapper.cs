﻿namespace SimpleBooks.Models.ModelMapper.Purchases
{
    public static class PurchaseOrderMapper
    {
        public static CreatePurchaseOrderViewModel ToCreateDto(this PurchaseOrderModel entity)
        {
            CreatePurchaseOrderViewModel viewModel = new CreatePurchaseOrderViewModel()
            {
                PurchaseOrderId = entity.PurchaseOrderId,
                PurchaseOrderDate = entity.PurchaseOrderDate,
                PurchaseOrderDueDate = entity.PurchaseOrderDueDate,
                VendorId = entity.VendorId,
                VendorTypeId = entity.VendorTypeId,
                PaymentTermId = entity.PaymentTermId,
                PurchaseOrderLines = entity.PurchaseOrderLines.Select(i => i.ToCreateDto()).ToList(),
            };
            return viewModel;
        }

        public static PurchaseOrderModel ToEntity(this CreatePurchaseOrderViewModel entity)
        {
            PurchaseOrderModel model = new PurchaseOrderModel()
            {
                PurchaseOrderId = entity.PurchaseOrderId,
                PurchaseOrderDate = entity.PurchaseOrderDate,
                PurchaseOrderDueDate = entity.PurchaseOrderDueDate,
                VendorId = entity.VendorId,
                VendorTypeId = entity.VendorTypeId,
                PaymentTermId = entity.PaymentTermId,
                PurchaseOrderLines = entity.PurchaseOrderLines.Select(i => i.ToEntity()).ToList(),
            };
            return model;
        }

        public static UpdatePurchaseOrderViewModel ToUpdateDto(this PurchaseOrderModel entity)
        {
            UpdatePurchaseOrderViewModel viewModel = new UpdatePurchaseOrderViewModel()
            {
                Id = entity.Id,
                PurchaseOrderId = entity.PurchaseOrderId,
                PurchaseOrderDate = entity.PurchaseOrderDate,
                PurchaseOrderDueDate = entity.PurchaseOrderDueDate,
                VendorId = entity.VendorId,
                VendorTypeId = entity.VendorTypeId,
                PaymentTermId = entity.PaymentTermId,
                PurchaseOrderLines = entity.PurchaseOrderLines.Select(i => i.ToUpdateDto()).ToList(),
            };
            return viewModel;
        }

        public static PurchaseOrderModel ToEntity(this UpdatePurchaseOrderViewModel entity)
        {
            PurchaseOrderModel model = new PurchaseOrderModel()
            {
                Id = entity.Id,
                PurchaseOrderId = entity.PurchaseOrderId,
                PurchaseOrderDate = entity.PurchaseOrderDate,
                PurchaseOrderDueDate = entity.PurchaseOrderDueDate,
                VendorId = entity.VendorId,
                VendorTypeId = entity.VendorTypeId,
                PaymentTermId = entity.PaymentTermId,
                PurchaseOrderLines = entity.PurchaseOrderLines.Select(i => i.ToEntity()).ToList(),
            };
            return model;
        }
    }
}
