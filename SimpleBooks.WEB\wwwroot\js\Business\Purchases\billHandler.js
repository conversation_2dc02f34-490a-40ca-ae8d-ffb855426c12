﻿import { vendorTools } from '/js/business/Purchases/vendorTools.js';
import { paymentTermTools } from '/js/business/Treasury/paymentTermTools.js';

export class BillHandler {
    constructor(vendors, paymentTerms) {
        this.vendors = vendors;
        this.paymentTerms = paymentTerms;
        this.openPurchaseOrders = [];
    }

    onVendorChanged(vendorId) {
        const vendor = this.vendors?.$values?.find(v => v.Id == vendorId);
        if (vendor) {
            vendorTools.updateVendor(vendor);
            this.checkOpenPurchaseOrders(vendorId);
        }
    }

    onPaymentTermChanged(paymentTermId) {
        const term = this.paymentTerms?.$values?.find(t => t.Id == paymentTermId);
        if (term) {
            paymentTermTools.updatePaymentTerm(term, "BillDueDate");
        }
    }

    async checkOpenPurchaseOrders(vendorId) {
        try {
            const url = `/Bill/GetOpenPurchaseOrdersByVendor?vendorId=${vendorId}`;
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const result = await response.json();
            if (result.success && result.data && result.data.$values && result.data.$values.length > 0) {
                this.openPurchaseOrders = result.data.$values;
                this.showPurchaseOrderSelection();
            } else {
                this.hidePurchaseOrderSelection();
            }
        } catch (error) {
            console.error('Error checking open purchase orders:', error);
            console.error('Error details:', error.message);
        }
    }

    showPurchaseOrderSelection() {
        // Remove existing container if it exists
        let purchaseOrderContainer = document.getElementById('purchaseOrderContainer');
        if (purchaseOrderContainer) {
            purchaseOrderContainer.remove();
        }

        // Create new container
        purchaseOrderContainer = document.createElement('div');
        purchaseOrderContainer.id = 'purchaseOrderContainer';
        purchaseOrderContainer.className = 'alert alert-info mt-3';

        // Find the vendor select element
        const vendorSelect = document.querySelector('select[asp-for="VendorId"]');

        if (vendorSelect) {
            // Insert after the vendor select's parent div
            const vendorSelectParent = vendorSelect.closest('.form-group');
            if (vendorSelectParent) {
                vendorSelectParent.insertAdjacentElement('afterend', purchaseOrderContainer);
            } else {
                // Fallback: insert after the vendor select itself
                vendorSelect.insertAdjacentElement('afterend', purchaseOrderContainer);
            }
        } else {
            // Fallback: append to the form
            const form = document.querySelector('form');
            if (form) {
                form.insertBefore(purchaseOrderContainer, form.firstChild);
            }
        }

        // Set the content
        purchaseOrderContainer.innerHTML = `
            <div class="row">
                <div class="col-md-12">
                    <h6><i class="bi bi-info-circle"></i> Open Purchase Orders Found</h6>
                    <p>This vendor has ${this.openPurchaseOrders.length} open purchase order(s). You can select one to populate the bill items.</p>
                    <div class="mb-3">
                        <label class="form-label">Select Purchase Order:</label>
                        <select id="purchaseOrderSelect" class="form-select" onchange="handler.onPurchaseOrderSelected(this.value)">
                            <option value="">-- Select a Purchase Order --</option>
                            ${this.openPurchaseOrders.map(so => `
                                <option value="${so.Id}">${so.PurchaseOrderId} - ${so.PurchaseOrderDate} (${so.PurchaseOrderLines?.$values.length || 0} items)</option>
                            `).join('')}
                        </select>
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="handler.populateFromPurchaseOrder()">
                        <i class="bi bi-arrow-down-circle"></i> Populate Bill Items
                    </button>
                </div>
            </div>
        `;
    }

    hidePurchaseOrderSelection() {
        const purchaseOrderContainer = document.getElementById('purchaseOrderContainer');
        if (purchaseOrderContainer) {
            purchaseOrderContainer.remove();
        }
    }

    async onPurchaseOrderSelected(purchaseOrderId) {
        if (!purchaseOrderId) return;

        try {
            const response = await fetch(`/Bill/GetOpenPurchaseOrderLines?purchaseOrderId=${purchaseOrderId}`);
            const result = await response.json();

            if (result.success && result.data) {
                this.selectedPurchaseOrderLines = result.data;
            }
        } catch (error) {
            console.error('Error loading purchase order lines:', error);
        }
    }

    populateFromPurchaseOrder() {
        if (!this.selectedPurchaseOrderLines || !this.selectedPurchaseOrderLines.$values || this.selectedPurchaseOrderLines.$values.length === 0) {
            alert('Please select a purchase order first.');
            return;
        }

        // Clear existing inventory items
        const inventoryContainer = document.querySelector('[data-inventory-container]');
        if (inventoryContainer) {
            inventoryContainer.innerHTML = '';
        }

        // Add inventory items from purchase order lines
        let addedCount = 0;
        let duplicateCount = 0;
        
        this.selectedPurchaseOrderLines.$values.forEach((line, index) => {
            const success = this.addInventoryItemFromPurchaseOrderLine(line, index);
            if (success) {
                addedCount++;
            } else {
                duplicateCount++;
            }
        });

        // Hide the purchase order selection after populating
        this.hidePurchaseOrderSelection();

        let message = `Successfully added ${addedCount} items from the selected purchase order.`;
        if (duplicateCount > 0) {
            message += ` ${duplicateCount} items were skipped because they were already added.`;
        }
        alert(message);
    }

    addInventoryItemFromPurchaseOrderLine(line, index) {
        // This method should be called by the inventory handler
        if (window.inventoryHandler) {
            return window.inventoryHandler.addInventoryItemFromPurchaseOrderLine(line, index);
        }
        return false;
    }
}
