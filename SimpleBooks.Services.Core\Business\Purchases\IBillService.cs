﻿namespace SimpleBooks.Services.Core.Business.Purchases
{
    public interface IBillService : ISimpleBooksBaseService<BillModel, IndexBillViewModel, CreateBillViewModel, UpdateBillViewModel>
    {
        Task<ServiceResult<IEnumerable<VendorDto>>> SelectiveVendorListAsync();
        Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveVendorTypeListAsync();
        Task<ServiceResult<IEnumerable<PaymentTermDto>>> SelectivePaymentTermListAsync();
        Task<ServiceResult<IEnumerable<ProductModel>>> SelectiveProductListAsync();
        Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveUnitListAsync();
        Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveStoreListAsync();
        Task<ServiceResult<IEnumerable<TaxTypeDto>>> TaxTypeListAsync();
        Task<ServiceResult<IEnumerable<TaxSubTypeDto>>> TaxSubTypeListAsync();
        Task<ServiceResult<IEnumerable<OpenPurchaseOrderDto>>> GetOpenPurchaseOrdersByVendorAsync(Ulid vendorId);
        Task<ServiceResult<IEnumerable<OpenPurchaseOrderLineDto>>> GetOpenPurchaseOrderLinesAsync(Ulid purchaseOrderId);
    }
}
