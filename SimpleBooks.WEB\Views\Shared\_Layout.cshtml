﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] | SimpleBooks</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <!-- Start Add Theme https://bootswatch.com/flatly/ -->
    <link rel="stylesheet" href="~/css/bootstrap.min.css" />
    <!-- End Add Theme https://bootswatch.com/flatly/ -->
    <!-- Start Add BootStrap-Icons -->
    <link rel="stylesheet" href="~/lib/bootstrap-icons/font/bootstrap-icons.min.css" />
    <!-- End Add BootStrap-Icons -->
    <!-- Start Add Google Fonts https://fonts.google.com/specimen/Chakra+Petch?query=chakr -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Chakra+Petch:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap">
    <!-- End Add Google Fonts https://fonts.google.com/specimen/Chakra+Petch?query=chakr -->
    <!-- Start Add SweetAlert2 -->
    <link rel="stylesheet" href="~/lib/sweetalert2/sweetalert2.min.css" />
    <!-- End Add SweetAlert2 -->
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/SimpleBooks.WEB.styles.css" asp-append-version="true" />
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg bg-primary mb-3" data-bs-theme="dark">
            <div class="container-fluid">
                <a class="navbar-brand" asp-area="" asp-controller="Home" asp-action="Index">SimpleBooks</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarColor01" aria-controls="navbarColor01" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarColor01">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link active" asp-area="" asp-controller="Home" asp-action="Index">
                                Home
                                <span class="visually-hidden">(current)</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-dark" asp-area="" asp-controller="Home" asp-action="Privacy">Privacy</a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" data-bs-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">HR</a>
                            <div class="dropdown-menu">
                                <a class="dropdown-item" asp-area="" asp-controller="Employee" asp-action="Index">Employee</a>
                            </div>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" data-bs-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">Warehouse</a>
                            <div class="dropdown-menu">
                                <a class="dropdown-item" asp-area="" asp-controller="ProductType" asp-action="Index">Product Type</a>
                                <a class="dropdown-item" asp-area="" asp-controller="ProductCategory" asp-action="Index">Product Category</a>
                                <a class="dropdown-item" asp-area="" asp-controller="Unit" asp-action="Index">Unit</a>
                                <a class="dropdown-item" asp-area="" asp-controller="Store" asp-action="Index">Store</a>
                                <a class="dropdown-item" asp-area="" asp-controller="Product" asp-action="Index">Product</a>
                            </div>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" data-bs-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">Purchases</a>
                            <div class="dropdown-menu">
                                <a class="dropdown-item" asp-area="" asp-controller="Vendor" asp-action="Index">Vendor</a>
                                <a class="dropdown-item" asp-area="" asp-controller="VendorType" asp-action="Index">Vendor Type</a>
                                <a class="dropdown-item" asp-area="" asp-controller="PurchaseOrder" asp-action="Index">Purchase Order</a>
                                <a class="dropdown-item" asp-area="" asp-controller="Bill" asp-action="Index">Bill</a>
                                <a class="dropdown-item" asp-area="" asp-controller="BillReturn" asp-action="Index">Bill Return</a>
                            </div>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" data-bs-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">Sales</a>
                            <div class="dropdown-menu">
                                <a class="dropdown-item" asp-area="" asp-controller="Customer" asp-action="Index">Customer</a>
                                <a class="dropdown-item" asp-area="" asp-controller="CustomerType" asp-action="Index">Customer Type</a>
                                <a class="dropdown-item" asp-area="" asp-controller="SalesOrder" asp-action="Index">Sales Order</a>
                                <a class="dropdown-item" asp-area="" asp-controller="Invoice" asp-action="Index">Invoice</a>
                                <a class="dropdown-item" asp-area="" asp-controller="InvoiceReturn" asp-action="Index">Invoice Return</a>
                            </div>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" data-bs-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">Treasury</a>
                            <div class="dropdown-menu">
                                <a class="dropdown-item" asp-area="" asp-controller="Drawer" asp-action="Index">Drawer</a>
                                <a class="dropdown-item" asp-area="" asp-controller="CashTreasuryVoucher" asp-action="Index">Cash Treasury Voucher</a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" asp-area="" asp-controller="Bank" asp-action="Index">Bank</a>
                                <a class="dropdown-item" asp-area="" asp-controller="BankTransferTreasuryVoucher" asp-action="Index">Bank Transfer Treasury Voucher</a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" asp-area="" asp-controller="CheckVault" asp-action="Index">Check Vault</a>
                                <a class="dropdown-item" asp-area="" asp-controller="CheckTreasuryVoucher" asp-action="Index">Check Treasury Voucher</a>
                                <a class="dropdown-item" asp-area="" asp-controller="CheckDeposit" asp-action="Index">Check Deposit</a>
                                <a class="dropdown-item" asp-area="" asp-controller="CheckCollection" asp-action="Index">Check Collection</a>
                                <a class="dropdown-item" asp-area="" asp-controller="CheckReject" asp-action="Index">Check Reject</a>
                                <a class="dropdown-item" asp-area="" asp-controller="CheckReturn" asp-action="Index">Check Return</a>
                                <a class="dropdown-item" asp-area="" asp-controller="CheckClear" asp-action="Index">Check Clear</a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" asp-area="" asp-controller="Expenses" asp-action="Index">Expenses</a>
                                <a class="dropdown-item" asp-area="" asp-controller="PaymentTerm" asp-action="Index">Payment Term</a>
                            </div>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" data-bs-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">Users</a>
                            <div class="dropdown-menu">
                                <a class="dropdown-item" asp-area="" asp-controller="Setting" asp-action="Index">Setting</a>
                                <a class="dropdown-item" asp-area="" asp-controller="User" asp-action="Index">User</a>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>
    <div class="container">
        <main role="main" class="pb-3">
            @RenderBody()
        </main>
    </div>

    <footer class="border-top footer text-muted">
        <div class="container">
            &copy; 2025 - SimpleBooks.WEB - <a asp-area="" asp-controller="Home" asp-action="Privacy">Privacy</a>
        </div>
    </footer>
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
