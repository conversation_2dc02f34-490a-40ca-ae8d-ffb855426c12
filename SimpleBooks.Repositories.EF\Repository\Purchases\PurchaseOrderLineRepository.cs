﻿namespace SimpleBooks.Repositories.EF.Repository.Purchases
{
    internal class PurchaseOrderLineRepository : SimpleBooksBaseRepository<PurchaseOrderLineModel, PurchaseOrderLineModel>
    {
        public PurchaseOrderLineRepository(ApplicationDBContext context) : base(context)
        {
        }

        protected override Expression<Func<PurchaseOrderLineModel, object>> OrderByColumn => x => x.Id;
    }
}
