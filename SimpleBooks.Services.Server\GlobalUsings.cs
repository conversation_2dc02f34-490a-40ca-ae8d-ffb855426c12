﻿global using GMCadiomCore.Models.BaseModels;
global using GMCadiomCore.Models.Enumerations;
global using GMCadiomCore.Models.ResultPattern;
global using GMCadiomCore.Models.ViewModel;
global using GMCadiomCore.Repositories.Factory;
global using GMCadiomCore.Repositories.IRepository;
global using GMCadiomCore.Services.Server;
global using Microsoft.EntityFrameworkCore;
global using Microsoft.EntityFrameworkCore.Query;
global using Microsoft.Extensions.DependencyInjection;
global using Microsoft.IdentityModel.Tokens;
global using Newtonsoft.Json;
global using SimpleBooks.Models.Enumerations;
global using SimpleBooks.Models.JsonConverters;
global using SimpleBooks.Models.Model.HR;
global using SimpleBooks.Models.Model.Purchases;
global using SimpleBooks.Models.Model.Sales;
global using SimpleBooks.Models.Model.Tax;
global using SimpleBooks.Models.Model.Treasury;
global using SimpleBooks.Models.Model.Treasury.BankManagement;
global using SimpleBooks.Models.Model.Treasury.BankManagement.BankTransferManagement;
global using SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement;
global using SimpleBooks.Models.Model.Treasury.CashManagement;
global using SimpleBooks.Models.Model.User;
global using SimpleBooks.Models.Model.Warehouse;
global using SimpleBooks.Models.ModelDTO.Authentication;
global using SimpleBooks.Models.ModelDTO.Purchases;
global using SimpleBooks.Models.ModelDTO.Sales;
global using SimpleBooks.Models.ModelDTO.Tax;
global using SimpleBooks.Models.ModelDTO.Treasury;
global using SimpleBooks.Models.ViewModel.BaseModels;
global using SimpleBooks.Models.ViewModel.HR.Employee;
global using SimpleBooks.Models.ViewModel.Purchases.Bill;
global using SimpleBooks.Models.ViewModel.Purchases.BillReturn;
global using SimpleBooks.Models.ViewModel.Purchases.Vendor;
global using SimpleBooks.Models.ViewModel.Purchases.VendorType;
global using SimpleBooks.Models.ViewModel.Sales.Customer;
global using SimpleBooks.Models.ViewModel.Sales.CustomerType;
global using SimpleBooks.Models.ViewModel.Sales.Invoice;
global using SimpleBooks.Models.ViewModel.Sales.InvoiceReturn;
global using SimpleBooks.Models.ViewModel.Sales.SalesOrder;
global using SimpleBooks.Models.ViewModel.Purchases.PurchaseOrder;
global using SimpleBooks.Models.ViewModel.Tax.TaxSubType;
global using SimpleBooks.Models.ViewModel.Tax.TaxType;
global using SimpleBooks.Models.ViewModel.Treasury.BankManagement.Bank;
global using SimpleBooks.Models.ViewModel.Treasury.BankManagement.BankAccount;
global using SimpleBooks.Models.ViewModel.Treasury.BankManagement.BankTransferManagement.BankTransferTreasuryVoucher;
global using SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckClear;
global using SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckCollection;
global using SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckDeposit;
global using SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckReject;
global using SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckReturn;
global using SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckStatusHistory;
global using SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckTreasuryVoucher;
global using SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckVault;
global using SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckVaultLocation;
global using SimpleBooks.Models.ViewModel.Treasury.CashManagement.CashTreasuryVoucher;
global using SimpleBooks.Models.ViewModel.Treasury.CashManagement.Drawer;
global using SimpleBooks.Models.ViewModel.Treasury.CashManagement.DrawerLocation;
global using SimpleBooks.Models.ViewModel.Treasury.Expenses;
global using SimpleBooks.Models.ViewModel.Treasury.PaymentTerm;
global using SimpleBooks.Models.ViewModel.Treasury.TreasuryLine;
global using SimpleBooks.Models.ViewModel.User.Setting;
global using SimpleBooks.Models.ViewModel.User.User;
global using SimpleBooks.Models.ViewModel.Warehouse.Inventory;
global using SimpleBooks.Models.ViewModel.Warehouse.InventoryTax;
global using SimpleBooks.Models.ViewModel.Warehouse.Product;
global using SimpleBooks.Models.ViewModel.Warehouse.ProductCategory;
global using SimpleBooks.Models.ViewModel.Warehouse.ProductTax;
global using SimpleBooks.Models.ViewModel.Warehouse.ProductType;
global using SimpleBooks.Models.ViewModel.Warehouse.ProductUnit;
global using SimpleBooks.Models.ViewModel.Warehouse.Store;
global using SimpleBooks.Models.ViewModel.Warehouse.Unit;
global using SimpleBooks.PermissionAndSession;
global using SimpleBooks.PermissionAndSession.Authentication;
global using SimpleBooks.PermissionAndSession.DI;
global using SimpleBooks.Repositories.Core.IFactory;
global using SimpleBooks.Repositories.EF.Factory;
global using SimpleBooks.Repositories.EF.Factory.Contexts;
global using SimpleBooks.Repositories.EF.Factory.Contexts.Interceptors;
global using SimpleBooks.Repositories.EF.Factory.DatabaseManager;
global using SimpleBooks.Services.Core.Authentication;
global using SimpleBooks.Services.Core.BaseService;
global using SimpleBooks.Services.Core.Business.HR;
global using SimpleBooks.Services.Core.Business.Purchases;
global using SimpleBooks.Services.Core.Business.Sales;
global using SimpleBooks.Services.Core.Business.Tax;
global using SimpleBooks.Services.Core.Business.Treasury;
global using SimpleBooks.Services.Core.Business.Treasury.BankManagement;
global using SimpleBooks.Services.Core.Business.Treasury.BankManagement.BankTransferManagement;
global using SimpleBooks.Services.Core.Business.Treasury.BankManagement.CheckManagement;
global using SimpleBooks.Services.Core.Business.Treasury.CashManagement;
global using SimpleBooks.Services.Core.Business.User;
global using SimpleBooks.Services.Core.Business.Warehouse;
global using SimpleBooks.Services.Server.Authentication;
global using SimpleBooks.Services.Server.BaseService;
global using SimpleBooks.Services.Server.Business.HR;
global using SimpleBooks.Services.Server.Business.Purchases;
global using SimpleBooks.Services.Server.Business.Sales;
global using SimpleBooks.Services.Server.Business.Tax;
global using SimpleBooks.Services.Server.Business.Treasury;
global using SimpleBooks.Services.Server.Business.Treasury.BankManagement;
global using SimpleBooks.Services.Server.Business.Treasury.BankManagement.BankTransferManagement;
global using SimpleBooks.Services.Server.Business.Treasury.BankManagement.CheckManagement;
global using SimpleBooks.Services.Server.Business.Treasury.CashManagement;
global using SimpleBooks.Services.Server.Business.User;
global using SimpleBooks.Services.Server.Business.Warehouse;
global using SimpleBooks.Shared.Helper;
global using System.ComponentModel.DataAnnotations;
global using System.IdentityModel.Tokens.Jwt;
global using System.Security.Claims;
global using System.Security.Cryptography;
global using System.Text;
