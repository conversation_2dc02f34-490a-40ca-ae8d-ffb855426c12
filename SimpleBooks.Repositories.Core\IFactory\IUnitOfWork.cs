﻿namespace SimpleBooks.Repositories.Core.IFactory
{
    public interface IUnitOfWork : IBaseUnitOfWork
    {
        #region HR
        IBaseRepository<EmployeeModel, EmployeeModel> Employee { get; }
        #endregion

        #region Purchases
        IBaseRepository<VendorModel, IndexVendorViewModel> Vendor { get; }
        IBaseRepository<VendorTypeModel, VendorTypeModel> VendorType { get; }
        IBaseRepository<BillModel, IndexBillViewModel> Bill { get; }
        IBaseRepository<BillReturnModel, IndexBillReturnViewModel> BillReturn { get; }
        IPurchaseOrderRepository<PurchaseOrderModel, IndexPurchaseOrderViewModel> PurchaseOrder { get; }
        IBaseRepository<PurchaseOrderLineModel, PurchaseOrderLineModel> PurchaseOrderLine { get; }
        #endregion

        #region Sales
        IBaseRepository<CustomerModel, IndexCustomerViewModel> Customer { get; }
        IBaseRepository<CustomerTypeModel, CustomerTypeModel> CustomerType { get; }
        IBaseRepository<InvoiceModel, IndexInvoiceViewModel> Invoice { get; }
        IBaseRepository<InvoiceReturnModel, IndexInvoiceReturnViewModel> InvoiceReturn { get; }
        ISalesOrderRepository<SalesOrderModel, IndexSalesOrderViewModel> SalesOrder { get; }
        IBaseRepository<SalesOrderLineModel, SalesOrderLineModel> SalesOrderLine { get; }
        #endregion

        #region Tax 
        ITaxTypeRepository<TaxTypeModel, TaxTypeModel> TaxType { get; }
        ITaxSubTypeRepository<TaxSubTypeModel, TaxSubTypeModel> TaxSubType { get; }
        #endregion

        #region Treasury 
        IBaseRepository<BankAccountModel, BankAccountModel> BankAccount { get; }
        IBaseRepository<BankModel, BankModel> Bank { get; }
        IBankTransferTreasuryVoucherRepository<BankTransferTreasuryVoucherModel, BankTransferTreasuryVoucherModel> BankTransferTreasuryVoucher { get; }
        ICheckTreasuryVoucherRepository<CheckTreasuryVoucherModel, CheckTreasuryVoucherModel> CheckTreasuryVoucher { get; }
        IBaseRepository<CheckClearModel, CheckClearModel> CheckClear { get; }
        IBaseRepository<CheckCollectionModel, CheckCollectionModel> CheckCollection { get; }
        IBaseRepository<CheckDepositModel, CheckDepositModel> CheckDeposit { get; }
        IBaseRepository<CheckRejectModel, CheckRejectModel> CheckReject { get; }
        IBaseRepository<CheckReturnModel, CheckReturnModel> CheckReturn { get; }
        IBaseRepository<CheckStatusHistoryModel, CheckStatusHistoryModel> CheckStatusHistory { get; }
        IBaseRepository<CheckVaultLocationModel, CheckVaultLocationModel> CheckVaultLocation { get; }
        IBaseRepository<CheckVaultModel, CheckVaultModel> CheckVault { get; }
        ICashTreasuryVoucherRepository<CashTreasuryVoucherModel, CashTreasuryVoucherModel> CashTreasuryVoucher { get; }
        IBaseRepository<DrawerLocationModel, DrawerLocationModel> DrawerLocation { get; }
        IBaseRepository<DrawerModel, DrawerModel> Drawer { get; }
        IBaseRepository<ExpensesModel, ExpensesModel> Expenses { get; }
        IPaymentTermRepository<PaymentTermModel, PaymentTermModel> PaymentTerm { get; }
        ITreasuryLineRepository<TreasuryLineModel, TreasuryLineModel> TreasuryLine { get; }
        #endregion

        #region User
        IAuditRepository<AuditModel> Audit { get; }
        IBaseRepository<ScreensAccessProfileModel, ScreensAccessProfileModel> ScreensAccessProfile { get; }
        IRefreshTokenRepository<RefreshTokenModel, RefreshTokenModel> RefreshToken { get; }
        IBaseRepository<SettingModel, SettingModel> Setting { get; }
        IUserRepository<UserModel, UserModel> User { get; }
        #endregion

        #region Warehouse
        IBaseRepository<InventoryModel, InventoryModel> Inventory { get; }
        IBaseRepository<InventoryTaxModel, InventoryTaxModel> InventoryTax { get; }
        IBaseRepository<ProductCategoryModel, ProductCategoryModel> ProductCategory { get; }
        IBaseRepository<ProductModel, IndexProductViewModel> Product { get; }
        IBaseRepository<ProductTaxModel, ProductTaxModel> ProductTax { get; }
        IBaseRepository<ProductTypeModel, ProductTypeModel> ProductType { get; }
        IBaseRepository<ProductUnitModel, ProductUnitModel> ProductUnit { get; }
        IBaseRepository<StoreModel, StoreModel> Store { get; }
        IBaseRepository<UnitModel, UnitModel> Unit { get; }
        #endregion

        IBaseRepository<TransactionTypeModel, TransactionTypeModel> TransactionType { get; }
    }
}
