﻿namespace SimpleBooks.Services.Server.Business.Purchases
{
    public class BillService : SimpleBooksBaseService<BillModel, IndexBillViewModel, CreateBillViewModel, UpdateBillViewModel>, IBillService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IVendorService _vendorService;
        private readonly IVendorTypeService _vendorTypeService;
        private readonly IPaymentTermService _paymentTermService;
        private readonly IProductService _productService;
        private readonly IUnitService _unitService;
        private readonly IStoreService _storeService;
        private readonly ITaxTypeService _taxTypeService;
        private readonly ITaxSubTypeService _taxSubTypeService;

        public BillService(
            IAuthenticationValidationService authenticationValidationService,
            IUnitOfWork unitOfWork,
            IVendorService vendorService,
            IVendorTypeService vendorTypeService,
            IPaymentTermService paymentTermService,
            IProductService productService,
            IUnitService unitService,
            IStoreService storeService,
            ITaxTypeService taxTypeService,
            ITaxSubTypeService taxSubTypeService) : base(authenticationValidationService, unitOfWork.Bill)
        {
            _unitOfWork = unitOfWork;
            _vendorService = vendorService;
            _vendorTypeService = vendorTypeService;
            _paymentTermService = paymentTermService;
            _productService = productService;
            _unitService = unitService;
            _storeService = storeService;
            _taxTypeService = taxTypeService;
            _taxSubTypeService = taxSubTypeService;
        }

        protected override Func<IQueryable<BillModel>, IIncludableQueryable<BillModel, object>>? Includes =>
            x => x
            .Include(xx => xx.Inventories).ThenInclude(xx => xx.InventoryTaxes);

        public override void EditModelBeforeSave(BillModel model)
        {
            base.EditModelBeforeSave(model);

            foreach (var inventory in model.Inventories)
            {
                inventory.BillId = model.Id;
                inventory.TransactionTypeId = TransactionTypeEnumeration.Bill.Value;
                inventory.CostAmount = inventory.Quantity * inventory.CostPrice;
                foreach (var tax in inventory.InventoryTaxes)
                {
                    tax.InventoryId = inventory.Id;
                    var taxType = TaxTypeEnumeration.FromValue(tax.TaxTypeId);
                    if (taxType.IsAddition)
                        tax.InventoryTaxsAmount = inventory.CostAmount * tax.InventoryTaxsRatio;
                    else
                        tax.InventoryTaxsAmount = inventory.CostAmount * tax.InventoryTaxsRatio * -1;
                }
                inventory.TaxAmount = inventory.InventoryTaxes.Sum(x => x.InventoryTaxsAmount);
                inventory.NetAmount = inventory.CostAmount + inventory.TaxAmount;
            }
        }

        public override void ValidateEntity(BillModel model, bool isEdit)
        {
            base.ValidateEntity(model, isEdit);

            if (model.Inventories.Count == 0)
                throw new ValidationException("At least one inventory item is required.");
            if (model.Inventories.Count >= 1)
            {
                var hasDuplicates = model.Inventories.GroupBy(x => x.ProductId).Any(g => g.Count() > 1);
                if (hasDuplicates)
                    throw new ValidationException("Inventory items must be unique.");
                var hasInvalidQuantity = model.Inventories.Any(x => x.Quantity <= 0);
                if (hasInvalidQuantity)
                    throw new ValidationException("Inventory quantity must be greater than zero.");
                var hasInvalidCostPrice = model.Inventories.Any(x => x.CostPrice < 0);
                if (hasInvalidCostPrice)
                    throw new ValidationException("Inventory cost price must be greater than or equal to zero.");
            }
        }

        public async Task<ServiceResult<IEnumerable<VendorDto>>> SelectiveVendorListAsync() => await _vendorService.SelectiveVendorDtoListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveVendorTypeListAsync() => await _vendorTypeService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<PaymentTermDto>>> SelectivePaymentTermListAsync() => await _paymentTermService.SelectivePaymentTermDtoListAsync();
        public async Task<ServiceResult<IEnumerable<ProductModel>>> SelectiveProductListAsync() => await _productService.GetAllAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveUnitListAsync() => await _unitService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveStoreListAsync() => await _storeService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<TaxTypeDto>>> TaxTypeListAsync() => await _taxTypeService.SelectiveTaxTypeDtoListAsync();
        public async Task<ServiceResult<IEnumerable<TaxSubTypeDto>>> TaxSubTypeListAsync() => await _taxSubTypeService.SelectiveTaxSubTypeDtoListAsync();

        public async Task<ServiceResult<IEnumerable<OpenPurchaseOrderDto>>> GetOpenPurchaseOrdersByVendorAsync(Ulid vendorId)
        {
            try
            {
                var result = await _unitOfWork.PurchaseOrder.GetOpenPurchaseOrdersByVendorAsync(vendorId);
                if (result == null)
                    return ServiceResult<IEnumerable<OpenPurchaseOrderDto>>.Failure("Purchase order not found.");
                return ServiceResult<IEnumerable<OpenPurchaseOrderDto>>.Success(result);
            }
            catch (Exception ex)
            {
                return ServiceResult<IEnumerable<OpenPurchaseOrderDto>>.Failure(ex.Message);
            }
        }

        public async Task<ServiceResult<IEnumerable<OpenPurchaseOrderLineDto>>> GetOpenPurchaseOrderLinesAsync(Ulid purchaseOrderId)
        {
            try
            {
                var result = await _unitOfWork.PurchaseOrder.GetOpenPurchaseOrderLinesAsync(purchaseOrderId);
                if (result == null)
                    return ServiceResult<IEnumerable<OpenPurchaseOrderLineDto>>.Failure("Purchase order not found.");
                return ServiceResult<IEnumerable<OpenPurchaseOrderLineDto>>.Success(result);
            }
            catch (Exception ex)
            {
                return ServiceResult<IEnumerable<OpenPurchaseOrderLineDto>>.Failure(ex.Message);
            }
        }
    }
}
