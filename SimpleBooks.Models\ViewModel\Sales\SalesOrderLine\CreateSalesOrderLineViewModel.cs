﻿namespace SimpleBooks.Models.ViewModel.Sales.SalesOrderLine
{
    public class CreateSalesOrderLineViewModel : BaseCreateViewModel, IEntityMapper<SalesOrderLineModel, CreateSalesOrderLineViewModel>
    {
        [CustomRequired]
        [DisplayName("Quantity")]
        public decimal Quantity { get; set; }
        [CustomRequired]
        [DisplayName("Price")]
        public decimal Price { get; set; }
        [CustomRequired]
        [DisplayName("Amount")]
        public decimal Amount { get; set; }
        [CustomRequired]
        [DisplayName("Unit Qty Ratio")]
        public decimal UnitQtyRatio { get; set; }

        [CustomRequired]
        [DisplayName("Product")]
        public Ulid ProductId { get; set; }
        [CustomRequired]
        [DisplayName("Product Unit")]
        public Ulid ProductUnitId { get; set; }
        [DisplayName("SalesOrder")]
        public Ulid SalesOrderId { get; set; }

        public CreateSalesOrderLineViewModel ToDto(SalesOrderLineModel entity) => entity.ToCreateDto();

        public SalesOrderLineModel ToEntity() => SalesOrderLineMapper.ToEntity(this);
    }
}
