﻿namespace SimpleBooks.API.Controllers.Business.Sales
{
    public class SalesOrderController : BaseBusinessController<SalesOrderModel, IndexSalesOrderViewModel, CreateSalesOrderViewModel, UpdateSalesOrderViewModel>
    {
        private readonly ISalesOrderService _salesOrderService;

        public SalesOrderController(ISalesOrderService salesOrderService) : base(salesOrderService)
        {
            _salesOrderService = salesOrderService;
        }
    }
}
