﻿namespace SimpleBooks.API.Controllers.Business.Purchases
{
    public class PurchaseOrderController : BaseBusinessController<PurchaseOrderModel, IndexPurchaseOrderViewModel, CreatePurchaseOrderViewModel, UpdatePurchaseOrderViewModel>
    {
        private readonly IPurchaseOrderService _purchaseOrderService;

        public PurchaseOrderController(IPurchaseOrderService purchaseOrderService) : base(purchaseOrderService)
        {
            _purchaseOrderService = purchaseOrderService;
        }
    }
}
