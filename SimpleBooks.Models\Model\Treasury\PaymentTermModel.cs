﻿namespace SimpleBooks.Models.Model.Treasury
{
    [Table("PaymentTerm")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<PaymentTermModel>))]
    public class PaymentTermModel : BaseWithoutTrackingModel
    {
        [CustomRequired]
        [DisplayName("Payment Term Name")]
        public string PaymentTermName { get; set; }
        [CustomRequired]
        [DisplayName("Discount Percentage")]
        [Range(0, 100)]
        public int DiscountPercentage { get; set; }

        [JsonIgnore]
        public virtual ICollection<CustomerModel> Customers { get; set; } = new List<CustomerModel>();
        [JsonIgnore]
        public virtual ICollection<InvoiceModel> Invoices { get; set; } = new List<InvoiceModel>();
        [JsonIgnore]
        public virtual ICollection<InvoiceReturnModel> InvoiceReturns { get; set; } = new List<InvoiceReturnModel>();
        [JsonIgnore]
        public virtual ICollection<SalesOrderModel> SalesOrders { get; set; } = new List<SalesOrderModel>();
        [JsonIgnore]
        public virtual ICollection<VendorModel> Vendors { get; set; } = new List<VendorModel>();
        [JsonIgnore]
        public virtual ICollection<BillModel> Bills { get; set; } = new List<BillModel>();
        [JsonIgnore]
        public virtual ICollection<BillReturnModel> BillReturns { get; set; } = new List<BillReturnModel>();
        [JsonIgnore]
        public virtual ICollection<PurchaseOrderModel> PurchaseOrders { get; set; } = new List<PurchaseOrderModel>();
    }

    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<PaymentTermStandardModel>))]
    public class PaymentTermStandardModel : PaymentTermModel
    {
        [CustomRequired]
        [DisplayName("Net Due Days")]
        [Range(0, 365)]
        public int NetDueDays { get; set; }
        [CustomRequired]
        [DisplayName("If Paid Within Days")]
        [Range(0, 365)]
        public int IfPaidWithinDays { get; set; }
    }

    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<PaymentTermDateDrivenModel>))]
    public class PaymentTermDateDrivenModel : PaymentTermModel
    {
        [CustomRequired]
        [DisplayName("Net Due Before Day Of Month")]
        [Range(0, 31)]
        public int NetDueBeforeDayOfMonth { get; set; }
        [CustomRequired]
        [DisplayName("Due Next Month Within Days")]
        [Range(0, 365)]
        public int DueNextMonthWithinDays { get; set; }
        [CustomRequired]
        [DisplayName("If Paid Before Day Of Month")]
        [Range(0, 31)]
        public int IfPaidBeforeDayOfMonth { get; set; }
    }
}
