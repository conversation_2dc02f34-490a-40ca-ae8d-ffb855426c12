﻿namespace SimpleBooks.WEB.Controllers.Business.Sales
{
    public class SalesOrderController : BaseBusinessController<
        SalesOrderModel,
        IndexSalesOrderViewModel,
        CreateSalesOrderViewModel,
        UpdateSalesOrderViewModel,
        IndexSalesOrderFormViewModel,
        CreateSalesOrderFormViewModel,
        UpdateSalesOrderFormViewModel>
    {
        private readonly ISalesOrderService _salesOrderService;

        public SalesOrderController(ISalesOrderService salesOrderService) : base(salesOrderService)
        {
            _salesOrderService = salesOrderService;
        }

        public override async Task<IActionResult> Create(CreateSalesOrderFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FullFillViewAsync(model);

            try
            {
                var result = await _salesOrderService.AddAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("salesOrder", ex.Message);
                return await FullFillViewAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        public override async Task<IActionResult> Update(Ulid id)
        {
            SalesOrderModel? entity = await _salesOrderService.GetByIdAsync(id);

            if (entity is null)
                return NotFound();

            UpdateSalesOrderFormViewModel viewModel = new UpdateSalesOrderFormViewModel()
            {
                Id = entity.Id,
                SalesOrderId = entity.SalesOrderId,
                SalesOrderDate = entity.SalesOrderDate,
                SalesOrderDueDate = entity.SalesOrderDueDate,
                CustomerId = entity.CustomerId,
                CustomerTypeId = entity.CustomerTypeId,
                CustomerRepId = entity.CustomerRepId,
                PaymentTermId = entity.PaymentTermId,
                SalesOrderLines = entity.SalesOrderLines.Select(x => x.ToUpdateDto()).ToList(),
                Customers = await _salesOrderService.SelectiveCustomerListAsync().GetDataOrThrowIfNullAsync(),
                CustomerTypes = await _salesOrderService.SelectiveCustomerTypeListAsync().ToSelectListItemAsync(),
                CustomerReps = await _salesOrderService.SelectiveCustomerRepListAsync().ToSelectListItemAsync(),
                PaymentTerms = await _salesOrderService.SelectivePaymentTermListAsync().GetDataOrThrowIfNullAsync(),
                Products = await _salesOrderService.SelectiveProductListAsync().GetDataOrThrowIfNullAsync(), 
                SelectiveProductUnits = await _salesOrderService.SelectiveUnitListAsync().ToSelectListItemAsync(),
            };

            return View(viewModel);
        }

        public override async Task<IActionResult> Update(UpdateSalesOrderFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FailedActionResultAsync(model);

            try
            {
                var result = await _salesOrderService.UpdateAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("salesOrder", ex.Message);
                return await FailedActionResultAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        protected override async Task<IActionResult> FullFillViewAsync(CreateSalesOrderFormViewModel model)
        {
            model.Customers = await _salesOrderService.SelectiveCustomerListAsync().GetDataOrThrowIfNullAsync();
            model.CustomerTypes = await _salesOrderService.SelectiveCustomerTypeListAsync().ToSelectListItemAsync();
            model.CustomerReps = await _salesOrderService.SelectiveCustomerRepListAsync().ToSelectListItemAsync();
            model.PaymentTerms = await _salesOrderService.SelectivePaymentTermListAsync().GetDataOrThrowIfNullAsync();
            model.Products = await _salesOrderService.SelectiveProductListAsync().GetDataOrThrowIfNullAsync();
            model.SelectiveProductUnits = await _salesOrderService.SelectiveUnitListAsync().ToSelectListItemAsync();
            return await Task.FromResult(View(model));
        }

        protected override async Task<IActionResult> FailedActionResultAsync(UpdateSalesOrderFormViewModel model)
        {
            model.Customers = await _salesOrderService.SelectiveCustomerListAsync().GetDataOrThrowIfNullAsync();
            model.CustomerTypes = await _salesOrderService.SelectiveCustomerTypeListAsync().ToSelectListItemAsync();
            model.CustomerReps = await _salesOrderService.SelectiveCustomerRepListAsync().ToSelectListItemAsync();
            model.PaymentTerms = await _salesOrderService.SelectivePaymentTermListAsync().GetDataOrThrowIfNullAsync();
            model.Products = await _salesOrderService.SelectiveProductListAsync().GetDataOrThrowIfNullAsync();
            model.SelectiveProductUnits = await _salesOrderService.SelectiveUnitListAsync().ToSelectListItemAsync();
            return await Task.FromResult(View(model));
        }
    }
}
