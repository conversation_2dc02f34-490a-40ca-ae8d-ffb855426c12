﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SimpleBooks.Repositories.EF.Migrations.SQLiteMigrations
{
    /// <inheritdoc />
    public partial class SimpleBooks_006 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<byte[]>(
                name: "PurchaseOrderId",
                table: "Inventory",
                type: "BLOB",
                nullable: true);

            migrationBuilder.AddColumn<byte[]>(
                name: "PurchaseOrderLineId",
                table: "Inventory",
                type: "BLOB",
                nullable: true);

            migrationBuilder.AddColumn<byte[]>(
                name: "SalesOrderId",
                table: "Inventory",
                type: "BLOB",
                nullable: true);

            migrationBuilder.AddColumn<byte[]>(
                name: "SalesOrderLineId",
                table: "Inventory",
                type: "BLOB",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "PurchaseOrder",
                columns: table => new
                {
                    Id = table.Column<byte[]>(type: "BLOB", nullable: false),
                    PurchaseOrderId = table.Column<string>(type: "TEXT", nullable: false),
                    PurchaseOrderDate = table.Column<DateOnly>(type: "Date", nullable: false),
                    PurchaseOrderDueDate = table.Column<DateOnly>(type: "Date", nullable: false),
                    VendorId = table.Column<byte[]>(type: "BLOB", nullable: false),
                    VendorTypeId = table.Column<byte[]>(type: "BLOB", nullable: true),
                    PaymentTermId = table.Column<byte[]>(type: "BLOB", nullable: true),
                    CreatedBy = table.Column<byte[]>(type: "BLOB", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    DeletedBy = table.Column<byte[]>(type: "BLOB", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    ModifaiedBy = table.Column<byte[]>(type: "BLOB", nullable: true),
                    ModifaiedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PurchaseOrder", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PurchaseOrder_PaymentTerm_PaymentTermId",
                        column: x => x.PaymentTermId,
                        principalTable: "PaymentTerm",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_PurchaseOrder_VendorType_VendorTypeId",
                        column: x => x.VendorTypeId,
                        principalTable: "VendorType",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_PurchaseOrder_Vendor_VendorId",
                        column: x => x.VendorId,
                        principalTable: "Vendor",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "SalesOrder",
                columns: table => new
                {
                    Id = table.Column<byte[]>(type: "BLOB", nullable: false),
                    SalesOrderId = table.Column<string>(type: "TEXT", nullable: false),
                    SalesOrderDate = table.Column<DateOnly>(type: "Date", nullable: false),
                    SalesOrderDueDate = table.Column<DateOnly>(type: "Date", nullable: false),
                    CustomerId = table.Column<byte[]>(type: "BLOB", nullable: false),
                    CustomerTypeId = table.Column<byte[]>(type: "BLOB", nullable: true),
                    CustomerRepId = table.Column<byte[]>(type: "BLOB", nullable: true),
                    PaymentTermId = table.Column<byte[]>(type: "BLOB", nullable: true),
                    CreatedBy = table.Column<byte[]>(type: "BLOB", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    DeletedBy = table.Column<byte[]>(type: "BLOB", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    ModifaiedBy = table.Column<byte[]>(type: "BLOB", nullable: true),
                    ModifaiedAt = table.Column<DateTime>(type: "datetime", nullable: true),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SalesOrder", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SalesOrder_CustomerType_CustomerTypeId",
                        column: x => x.CustomerTypeId,
                        principalTable: "CustomerType",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_SalesOrder_Customer_CustomerId",
                        column: x => x.CustomerId,
                        principalTable: "Customer",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_SalesOrder_Employee_CustomerRepId",
                        column: x => x.CustomerRepId,
                        principalTable: "Employee",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_SalesOrder_PaymentTerm_PaymentTermId",
                        column: x => x.PaymentTermId,
                        principalTable: "PaymentTerm",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "PurchaseOrderLine",
                columns: table => new
                {
                    Id = table.Column<byte[]>(type: "BLOB", nullable: false),
                    Quantity = table.Column<decimal>(type: "TEXT", precision: 18, scale: 5, nullable: false),
                    Price = table.Column<decimal>(type: "TEXT", precision: 18, scale: 5, nullable: false),
                    Amount = table.Column<decimal>(type: "TEXT", precision: 18, scale: 5, nullable: false),
                    UnitQtyRatio = table.Column<decimal>(type: "TEXT", precision: 18, scale: 5, nullable: false),
                    ProductId = table.Column<byte[]>(type: "BLOB", nullable: false),
                    ProductUnitId = table.Column<byte[]>(type: "BLOB", nullable: false),
                    PurchaseOrderId = table.Column<byte[]>(type: "BLOB", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PurchaseOrderLine", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PurchaseOrderLine_Product_ProductId",
                        column: x => x.ProductId,
                        principalTable: "Product",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_PurchaseOrderLine_PurchaseOrder_PurchaseOrderId",
                        column: x => x.PurchaseOrderId,
                        principalTable: "PurchaseOrder",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_PurchaseOrderLine_Unit_ProductUnitId",
                        column: x => x.ProductUnitId,
                        principalTable: "Unit",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "SalesOrderLine",
                columns: table => new
                {
                    Id = table.Column<byte[]>(type: "BLOB", nullable: false),
                    Quantity = table.Column<decimal>(type: "TEXT", precision: 18, scale: 5, nullable: false),
                    Price = table.Column<decimal>(type: "TEXT", precision: 18, scale: 5, nullable: false),
                    Amount = table.Column<decimal>(type: "TEXT", precision: 18, scale: 5, nullable: false),
                    UnitQtyRatio = table.Column<decimal>(type: "TEXT", precision: 18, scale: 5, nullable: false),
                    ProductId = table.Column<byte[]>(type: "BLOB", nullable: false),
                    ProductUnitId = table.Column<byte[]>(type: "BLOB", nullable: false),
                    SalesOrderId = table.Column<byte[]>(type: "BLOB", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SalesOrderLine", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SalesOrderLine_Product_ProductId",
                        column: x => x.ProductId,
                        principalTable: "Product",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_SalesOrderLine_SalesOrder_SalesOrderId",
                        column: x => x.SalesOrderId,
                        principalTable: "SalesOrder",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_SalesOrderLine_Unit_ProductUnitId",
                        column: x => x.ProductUnitId,
                        principalTable: "Unit",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_Inventory_PurchaseOrderId",
                table: "Inventory",
                column: "PurchaseOrderId");

            migrationBuilder.CreateIndex(
                name: "IX_Inventory_PurchaseOrderLineId",
                table: "Inventory",
                column: "PurchaseOrderLineId");

            migrationBuilder.CreateIndex(
                name: "IX_Inventory_SalesOrderId",
                table: "Inventory",
                column: "SalesOrderId");

            migrationBuilder.CreateIndex(
                name: "IX_Inventory_SalesOrderLineId",
                table: "Inventory",
                column: "SalesOrderLineId");

            migrationBuilder.CreateIndex(
                name: "IX_PurchaseOrder_PaymentTermId",
                table: "PurchaseOrder",
                column: "PaymentTermId");

            migrationBuilder.CreateIndex(
                name: "IX_PurchaseOrder_VendorId",
                table: "PurchaseOrder",
                column: "VendorId");

            migrationBuilder.CreateIndex(
                name: "IX_PurchaseOrder_VendorTypeId",
                table: "PurchaseOrder",
                column: "VendorTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_PurchaseOrderLine_ProductId",
                table: "PurchaseOrderLine",
                column: "ProductId");

            migrationBuilder.CreateIndex(
                name: "IX_PurchaseOrderLine_ProductUnitId",
                table: "PurchaseOrderLine",
                column: "ProductUnitId");

            migrationBuilder.CreateIndex(
                name: "IX_PurchaseOrderLine_PurchaseOrderId",
                table: "PurchaseOrderLine",
                column: "PurchaseOrderId");

            migrationBuilder.CreateIndex(
                name: "IX_SalesOrder_CustomerId",
                table: "SalesOrder",
                column: "CustomerId");

            migrationBuilder.CreateIndex(
                name: "IX_SalesOrder_CustomerRepId",
                table: "SalesOrder",
                column: "CustomerRepId");

            migrationBuilder.CreateIndex(
                name: "IX_SalesOrder_CustomerTypeId",
                table: "SalesOrder",
                column: "CustomerTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_SalesOrder_PaymentTermId",
                table: "SalesOrder",
                column: "PaymentTermId");

            migrationBuilder.CreateIndex(
                name: "IX_SalesOrderLine_ProductId",
                table: "SalesOrderLine",
                column: "ProductId");

            migrationBuilder.CreateIndex(
                name: "IX_SalesOrderLine_ProductUnitId",
                table: "SalesOrderLine",
                column: "ProductUnitId");

            migrationBuilder.CreateIndex(
                name: "IX_SalesOrderLine_SalesOrderId",
                table: "SalesOrderLine",
                column: "SalesOrderId");

            migrationBuilder.AddForeignKey(
                name: "FK_Inventory_PurchaseOrderLine_PurchaseOrderLineId",
                table: "Inventory",
                column: "PurchaseOrderLineId",
                principalTable: "PurchaseOrderLine",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Inventory_PurchaseOrder_PurchaseOrderId",
                table: "Inventory",
                column: "PurchaseOrderId",
                principalTable: "PurchaseOrder",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Inventory_SalesOrderLine_SalesOrderLineId",
                table: "Inventory",
                column: "SalesOrderLineId",
                principalTable: "SalesOrderLine",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Inventory_SalesOrder_SalesOrderId",
                table: "Inventory",
                column: "SalesOrderId",
                principalTable: "SalesOrder",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Inventory_PurchaseOrderLine_PurchaseOrderLineId",
                table: "Inventory");

            migrationBuilder.DropForeignKey(
                name: "FK_Inventory_PurchaseOrder_PurchaseOrderId",
                table: "Inventory");

            migrationBuilder.DropForeignKey(
                name: "FK_Inventory_SalesOrderLine_SalesOrderLineId",
                table: "Inventory");

            migrationBuilder.DropForeignKey(
                name: "FK_Inventory_SalesOrder_SalesOrderId",
                table: "Inventory");

            migrationBuilder.DropTable(
                name: "PurchaseOrderLine");

            migrationBuilder.DropTable(
                name: "SalesOrderLine");

            migrationBuilder.DropTable(
                name: "PurchaseOrder");

            migrationBuilder.DropTable(
                name: "SalesOrder");

            migrationBuilder.DropIndex(
                name: "IX_Inventory_PurchaseOrderId",
                table: "Inventory");

            migrationBuilder.DropIndex(
                name: "IX_Inventory_PurchaseOrderLineId",
                table: "Inventory");

            migrationBuilder.DropIndex(
                name: "IX_Inventory_SalesOrderId",
                table: "Inventory");

            migrationBuilder.DropIndex(
                name: "IX_Inventory_SalesOrderLineId",
                table: "Inventory");

            migrationBuilder.DropColumn(
                name: "PurchaseOrderId",
                table: "Inventory");

            migrationBuilder.DropColumn(
                name: "PurchaseOrderLineId",
                table: "Inventory");

            migrationBuilder.DropColumn(
                name: "SalesOrderId",
                table: "Inventory");

            migrationBuilder.DropColumn(
                name: "SalesOrderLineId",
                table: "Inventory");
        }
    }
}
