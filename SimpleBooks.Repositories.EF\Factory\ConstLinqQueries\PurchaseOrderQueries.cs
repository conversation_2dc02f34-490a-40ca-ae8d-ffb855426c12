﻿namespace SimpleBooks.Repositories.EF.Factory.ConstLinqQueries
{
    internal class PurchaseOrderQueries
    {
        public static IQueryable<IndexPurchaseOrderViewModel> PurchaseOrderViewQuery(ApplicationDBContext dbContext) => (from bill in dbContext.PurchaseOrders
                                                                                                       .Include(x => x.Vendor)
                                                                                                       .Include(x => x.VendorType)
                                                                                                       .Include(x => x.PaymentTerm)
                                                                                                       .Include(x => x.PurchaseOrderLines).AsNoTracking()
                                                                                                       select new IndexPurchaseOrderViewModel
                                                                                                       {
                                                                                                           Id = bill.Id,
                                                                                                           PurchaseOrderId = bill.PurchaseOrderId,
                                                                                                           PurchaseOrderDate = bill.PurchaseOrderDate,
                                                                                                           PurchaseOrderDueDate = bill.PurchaseOrderDueDate,
                                                                                                           VendorName = bill.Vendor != null ? bill.Vendor.VendorName : string.Empty,
                                                                                                           VendorTypeName = bill.VendorType != null ? bill.VendorType.VendorTypeName : string.Empty,
                                                                                                           PaymentTermName = bill.PaymentTerm != null ? bill.PaymentTerm.PaymentTermName : string.Empty,
                                                                                                           Amount = bill.PurchaseOrderLines.Sum(x => x.Amount),
                                                                                                       });
    }
}
