﻿namespace SimpleBooks.Models.ViewModel.Purchases.PurchaseOrderLine
{
    public class CreatePurchaseOrderLineViewModel : BaseCreateViewModel, IEntityMapper<PurchaseOrderLineModel, CreatePurchaseOrderLineViewModel>
    {
        [CustomRequired]
        [DisplayName("Quantity")]
        public decimal Quantity { get; set; }
        [CustomRequired]
        [DisplayName("Price")]
        public decimal Price { get; set; }
        [CustomRequired]
        [DisplayName("Amount")]
        public decimal Amount { get; set; }
        [CustomRequired]
        [DisplayName("Unit Qty Ratio")]
        public decimal UnitQtyRatio { get; set; }

        [CustomRequired]
        [DisplayName("Product")]
        public Ulid ProductId { get; set; }
        [CustomRequired]
        [DisplayName("Product Unit")]
        public Ulid ProductUnitId { get; set; }
        [DisplayName("PurchaseOrder")]
        public Ulid PurchaseOrderId { get; set; }

        public CreatePurchaseOrderLineViewModel ToDto(PurchaseOrderLineModel entity) => entity.ToCreateDto();

        public PurchaseOrderLineModel ToEntity() => PurchaseOrderLineMapper.ToEntity(this);
    }
}
