﻿namespace SimpleBooks.Repositories.EF.Repository.Treasury.BankManagement.CheckManagement
{
    internal class CheckRejectRepository : SimpleBooksBaseRepository<CheckRejectModel, CheckRejectModel>
    {
        public CheckRejectRepository(ApplicationDBContext context) : base(context)
        {
        }

        protected override Expression<Func<CheckRejectModel, object>> OrderByColumn => x => x.Id;

        public override async Task<CheckRejectModel?> AddAsync(CheckRejectModel? model)
        {
            if (model == null)
                throw new Exception("Model cannot be null");
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var newIds = model.CheckTreasuryVouchers.Select(x => x.Id).ToHashSet();

                var specs = new RepositorySpecifications<CheckTreasuryVoucherModel>
                {
                    Includes = x => x.Include(xx => xx.CheckStatusHistories),
                    SearchValue = x => newIds.Contains(x.Id) && x.CheckStatusId == CheckStatusEnumeration.Deposited.Value,
                    IsTackable = true,
                };

                var selectedChecks = await GetQueryable(specs).ToListAsync();

                var missingIds = newIds.Except(selectedChecks.Select(x => x.Id)).ToList();
                if (missingIds.Any())
                    throw new Exception($"Invalid check(s): {string.Join(", ", missingIds)}");

                foreach (var check in selectedChecks)
                {
                    check.CheckStatusId = CheckStatusEnumeration.Rejected.Value;
                    check.CheckRejectId = model.Id;

                    model.CheckStatusHistories.Add(new CheckStatusHistoryModel
                    {
                        TransactionDate = model.RejectDate,
                        Note = $"Check rejected on {model.RejectDate:dd/MM/yyyy}",
                        CheckStatusFromId = CheckStatusEnumeration.Deposited.Value,
                        CheckStatusToId = CheckStatusEnumeration.Rejected.Value,
                        CheckTreasuryVoucherId = check.Id,
                        CheckRejectId = model.Id
                    });
                }

                model.CheckTreasuryVouchers = selectedChecks;

                var result = await _context.Set<CheckRejectModel>().AddAsync(model);
                await _context.SaveChangesAsync();
                return result.Entity;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public override async Task<CheckRejectModel?> UpdateAsync(CheckRejectModel? model)
        {
            if (model == null)
                throw new Exception("Model cannot be null");
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var spec = new RepositorySpecifications<CheckRejectModel>
                {
                    Includes = x => x
                        .Include(d => d.CheckTreasuryVouchers)
                        .ThenInclude(c => c.CheckStatusHistories),
                    SearchValue = x => x.Id == model.Id,
                    IsTackable = true
                };

                var existingRejectQuery = GetQueryable(spec);

                var existingReject = await existingRejectQuery.FirstOrDefaultAsync();
                if (existingReject == null)
                    throw new Exception("Reject not found");
                var existingChecks = existingReject.CheckTreasuryVouchers.ToList();
                var newCheckIds = model.CheckTreasuryVouchers.Select(c => c.Id).ToHashSet();
                var oldCheckIds = existingChecks.Select(c => c.Id).ToHashSet();

                // 1. Checks to be removed
                var removedChecks = existingChecks.Where(c => !newCheckIds.Contains(c.Id)).ToList();

                foreach (var check in removedChecks)
                {
                    check.CheckStatusId = CheckStatusEnumeration.Deposited.Value;
                    check.CheckRejectId = null;

                    var rejectHistory = check.CheckStatusHistories
                        .FirstOrDefault(h => h.CheckRejectId == existingReject.Id && h.CheckStatusToId == CheckStatusEnumeration.Rejected.Value);

                    if (rejectHistory != null)
                        _context.Set<CheckStatusHistoryModel>().Remove(rejectHistory);
                }

                // 2. Checks to be added
                var addedCheckIds = newCheckIds.Except(oldCheckIds).ToList();
                var newCheckSpecs = new RepositorySpecifications<CheckTreasuryVoucherModel>
                {
                    Includes = x => x.Include(xx => xx.CheckStatusHistories),
                    SearchValue = x => addedCheckIds.Contains(x.Id) && x.CheckStatusId == CheckStatusEnumeration.Deposited.Value,
                    IsTackable = true
                };
                var addedChecks = GetQueryable(newCheckSpecs);

                foreach (var check in addedChecks)
                {
                    check.CheckStatusId = CheckStatusEnumeration.Rejected.Value;
                    check.CheckRejectId = existingReject.Id;

                    check.CheckStatusHistories.Add(new CheckStatusHistoryModel
                    {
                        TransactionDate = model.RejectDate,
                        Note = $"Check rejected on {model.RejectDate:dd/MM/yyyy}",
                        CheckStatusFromId = CheckStatusEnumeration.Deposited.Value,
                        CheckStatusToId = CheckStatusEnumeration.Rejected.Value,
                        CheckTreasuryVoucherId = check.Id,
                        CheckRejectId = existingReject.Id
                    });
                }

                // 3. Remaining checks that were not changed
                var retainedChecks = existingChecks.Where(c => newCheckIds.Contains(c.Id)).ToList();

                // 4. Final update: fully replace the list with only the updated list
                existingReject.RejectDate = model.RejectDate;
                existingReject.RejectReason = model.RejectReason;
                existingReject.RefranceNumber = model.RefranceNumber;
                existingReject.CheckTreasuryVouchers = retainedChecks.Concat(addedChecks).ToList();

                var result = _context.Set<CheckRejectModel>().Update(existingReject);
                await _context.SaveChangesAsync();
                return result.Entity;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }
    }
}
