﻿namespace SimpleBooks.Services.Server.Business.Purchases
{
    public class PurchaseOrderService : SimpleBooksBaseService<PurchaseOrderModel, IndexPurchaseOrderViewModel, CreatePurchaseOrderViewModel, UpdatePurchaseOrderViewModel>, IPurchaseOrderService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IVendorService _vendorService;
        private readonly IVendorTypeService _vendorTypeService;
        private readonly IPaymentTermService _paymentTermService;
        private readonly IProductService _productService;
        private readonly IUnitService _unitService;

        public PurchaseOrderService(
            IAuthenticationValidationService authenticationValidationService,
            IUnitOfWork unitOfWork,
            IVendorService vendorService,
            IVendorTypeService vendorTypeService,
            IPaymentTermService paymentTermService,
            IProductService productService,
            IUnitService unitService) : base(authenticationValidationService, unitOfWork.PurchaseOrder)
        {
            _unitOfWork = unitOfWork;
            _vendorService = vendorService;
            _vendorTypeService = vendorTypeService;
            _paymentTermService = paymentTermService;
            _productService = productService;
            _unitService = unitService;
        }

        protected override Func<IQueryable<PurchaseOrderModel>, IIncludableQueryable<PurchaseOrderModel, object>>? Includes =>
            x => x
            .Include(xx => xx.PurchaseOrderLines);

        public override void EditModelBeforeSave(PurchaseOrderModel model)
        {
            base.EditModelBeforeSave(model);

            foreach (var inventory in model.PurchaseOrderLines)
            {
                inventory.PurchaseOrderId = model.Id;
                inventory.Amount = inventory.Quantity * inventory.Price;
            }
        }

        public override void ValidateEntity(PurchaseOrderModel model, bool isEdit)
        {
            base.ValidateEntity(model, isEdit);

            if (model.PurchaseOrderLines.Count == 0)
                throw new ValidationException("At least one inventory item is required.");
            if (model.PurchaseOrderLines.Count >= 1)
            {
                var hasDuplicates = model.PurchaseOrderLines.GroupBy(x => x.ProductId).Any(g => g.Count() > 1);
                if (hasDuplicates)
                    throw new ValidationException("Inventory items must be unique.");
                var hasInvalidQuantity = model.PurchaseOrderLines.Any(x => x.Quantity <= 0);
                if (hasInvalidQuantity)
                    throw new ValidationException("Inventory quantity must be greater than zero.");
                var hasInvalidCostPrice = model.PurchaseOrderLines.Any(x => x.Price < 0);
                if (hasInvalidCostPrice)
                    throw new ValidationException("Inventory cost price must be greater than or equal to zero.");
            }
        }

        public async Task<ServiceResult<IEnumerable<VendorDto>>> SelectiveVendorListAsync() => await _vendorService.SelectiveVendorDtoListAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveVendorTypeListAsync() => await _vendorTypeService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<PaymentTermDto>>> SelectivePaymentTermListAsync() => await _paymentTermService.SelectivePaymentTermDtoListAsync();
        public async Task<ServiceResult<IEnumerable<ProductModel>>> SelectiveProductListAsync() => await _productService.GetAllAsync();
        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveUnitListAsync() => await _unitService.GetSelectListAsync();
    }
}
