﻿namespace SimpleBooks.Services.Core.Business.Sales
{
    public interface ISalesOrderService : ISimpleBooksBaseService<SalesOrderModel, IndexSalesOrderViewModel, CreateSalesOrderViewModel, UpdateSalesOrderViewModel>
    {
        Task<ServiceResult<IEnumerable<CustomerDto>>> SelectiveCustomerListAsync();
        Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveCustomerTypeListAsync();
        Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveCustomerRepListAsync();
        Task<ServiceResult<IEnumerable<PaymentTermDto>>> SelectivePaymentTermListAsync();
        Task<ServiceResult<IEnumerable<ProductModel>>> SelectiveProductListAsync();
        Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveUnitListAsync();
    }
}
