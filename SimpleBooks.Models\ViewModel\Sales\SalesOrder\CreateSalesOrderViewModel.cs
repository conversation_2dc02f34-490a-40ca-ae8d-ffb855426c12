﻿namespace SimpleBooks.Models.ViewModel.Sales.SalesOrder
{
    public class CreateSalesOrderViewModel : BaseCreateViewModel, IEntityMapper<SalesOrderModel, CreateSalesOrderViewModel>
    {
        [CustomRequired]
        [DisplayName("Sales Order Id")]
        public string SalesOrderId { get; set; }
        [CustomRequired]
        [DisplayName("Sales Order Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateOnly SalesOrderDate { get; set; }
        [CustomRequired]
        [DisplayName("Sales Order Due Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateOnly SalesOrderDueDate { get; set; }

        [CustomRequired]
        [DisplayName("Customer")]
        public Ulid CustomerId { get; set; }
        [DisplayName("Customer Type")]
        public Ulid? CustomerTypeId { get; set; }
        [DisplayName("Customer Sales Rep")]
        public Ulid? CustomerRepId { get; set; }
        [DisplayName("Payment Term")]
        public Ulid? PaymentTermId { get; set; }

        [CustomRequired]
        [DisplayName("Sales Order Lines")]
        public IList<CreateSalesOrderLineViewModel> SalesOrderLines { get; set; } = new List<CreateSalesOrderLineViewModel>();

        public CreateSalesOrderViewModel ToDto(SalesOrderModel entity) => entity.ToCreateDto();

        public SalesOrderModel ToEntity() => SalesOrderMapper.ToEntity(this);
    }
}
