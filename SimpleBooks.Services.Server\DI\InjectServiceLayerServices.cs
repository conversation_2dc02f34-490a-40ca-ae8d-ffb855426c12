﻿namespace SimpleBooks.Services.Server.DI
{
    public static class InjectServiceLayerServices
    {
        public static IServiceCollection InjectServiceServices(this IServiceCollection services)
        {
            services.InjectPermissionAndSessionServices();

            // Register MySQL DbContext
            services.AddDbContextFactory<MySQLApplicationDBContext>((serviceProvider, options) =>
            {
                DataBaseAppSettings dataBaseAppSettings = serviceProvider.GetRequiredService<DataBaseAppSettings>();
                options.UseMySql(dataBaseAppSettings.MySQL, ServerVersion.AutoDetect(dataBaseAppSettings.MySQL), mysqlOptions =>
                {
                    mysqlOptions.MigrationsAssembly("SimpleBooks.Repositories.EF");
                    mysqlOptions.EnableRetryOnFailure();
                    mysqlOptions.CommandTimeout(300);
                });
                options.AddInterceptors(new SimpleBooksSaveChangesInterceptor(serviceProvider));
            });

            // Register SQLite DbContext
            services.AddDbContextFactory<SQLiteApplicationDBContext>((serviceProvider, options) =>
            {
                DataBaseAppSettings dataBaseAppSettings = serviceProvider.GetRequiredService<DataBaseAppSettings>();
                options.UseSqlite($"Data Source={dataBaseAppSettings.SQLite}", sqliteOptions =>
                {
                    sqliteOptions.MigrationsAssembly("SimpleBooks.Repositories.EF");
                });
                options.AddInterceptors(new SimpleBooksSaveChangesInterceptor(serviceProvider));
            });

            services.AddHttpContextAccessor();
            services.AddSingleton<IDatabaseService, DatabaseService>();
            services.AddHostedService<DatabaseMonitorService>();
            services.AddScoped<IUnitOfWork, UnitOfWork>();
            services.AddScoped<IAuthenticationService, AuthenticationService>();
            #region HR
            services.AddScoped<IEmployeeService, EmployeeService>();
            #endregion
            #region Purchases
            services.AddScoped<IVendorService, VendorService>();
            services.AddScoped<IVendorTypeService, VendorTypeService>();
            services.AddScoped<IBillService, BillService>();
            services.AddScoped<IBillReturnService, BillReturnService>();
            services.AddScoped<IPurchaseOrderService, PurchaseOrderService>();
            #endregion
            #region Sales
            services.AddScoped<ICustomerService, CustomerService>();
            services.AddScoped<ICustomerTypeService, CustomerTypeService>();
            services.AddScoped<IInvoiceService, InvoiceService>();
            services.AddScoped<IInvoiceReturnService, InvoiceReturnService>();
            services.AddScoped<ISalesOrderService, SalesOrderService>();
            #endregion
            #region Tax
            services.AddScoped<ITaxTypeService, TaxTypeService>();
            services.AddScoped<ITaxSubTypeService, TaxSubTypeService>();
            #endregion
            #region Treasury
            services.AddScoped<IBankService, BankService>();
            services.AddScoped<IBankAccountService, BankAccountService>();
            services.AddScoped<IBankTransferTreasuryVoucherService, BankTransferTreasuryVoucherService>();
            services.AddScoped<ICheckClearService, CheckClearService>();
            services.AddScoped<ICheckCollectionService, CheckCollectionService>();
            services.AddScoped<ICheckDepositService, CheckDepositService>();
            services.AddScoped<ICheckRejectService, CheckRejectService>();
            services.AddScoped<ICheckReturnService, CheckReturnService>();
            services.AddScoped<ICheckStatusHistoryService, CheckStatusHistoryService>();
            services.AddScoped<ICheckTreasuryVoucherService, CheckTreasuryVoucherService>();
            services.AddScoped<ICheckVaultService, CheckVaultService>();
            services.AddScoped<ICheckVaultLocationService, CheckVaultLocationService>();
            services.AddScoped<ICashTreasuryVoucherService, CashTreasuryVoucherService>();
            services.AddScoped<IDrawerService, DrawerService>();
            services.AddScoped<IDrawerLocationService, DrawerLocationService>();
            services.AddScoped<IExpensesService, ExpensesService>();
            services.AddScoped<IPaymentTermService, PaymentTermService>();
            services.AddScoped<ITreasuryLineService, TreasuryLineService>();
            #endregion
            #region User
            services.AddScoped<ISettingService, SettingService>();
            services.AddScoped<IUserService, UserService>();
            #endregion
            #region Warehouse
            services.AddScoped<IInventoryService, InventoryService>();
            services.AddScoped<IProductCategoryService, ProductCategoryService>();
            services.AddScoped<IProductService, ProductService>();
            services.AddScoped<IProductTaxService, ProductTaxService>();
            services.AddScoped<IProductTypeService, ProductTypeService>();
            services.AddScoped<IProductUnitService, ProductUnitService>();
            services.AddScoped<IStoreService, StoreService>();
            services.AddScoped<IUnitService, UnitService>();
            #endregion

            return services;
        }
    }
}
