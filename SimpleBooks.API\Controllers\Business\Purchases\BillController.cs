﻿namespace SimpleBooks.API.Controllers.Business.Purchases
{
    public class BillController : BaseBusinessController<BillModel, IndexBillViewModel, CreateBillViewModel, UpdateBillViewModel>
    {
        private readonly IBillService _billService;

        public BillController(IBillService billService) : base(billService)
        {
            _billService = billService;
        }


        [HttpGet(nameof(GetOpenPurchaseOrdersByVendorAsync))]
        public async Task<IActionResult> GetOpenPurchaseOrdersByVendorAsync(string vendorId)
        {
            try
            {
                Ulid ulid = Ulid.Parse(vendorId);
                var result = await _billService.GetOpenPurchaseOrdersByVendorAsync(ulid);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpGet(nameof(GetOpenPurchaseOrderLinesAsync))]
        public async Task<IActionResult> GetOpenPurchaseOrderLinesAsync(string purchaseOrderId)
        {
            try
            {
                Ulid ulid = Ulid.Parse(purchaseOrderId);
                var result = await _billService.GetOpenPurchaseOrderLinesAsync(ulid);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }
    }
}
