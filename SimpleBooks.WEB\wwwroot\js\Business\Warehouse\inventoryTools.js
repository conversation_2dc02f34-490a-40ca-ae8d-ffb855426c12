﻿import { dataGridViewTools } from '/js/dataGridViewTools.js';
import { TaxValueModel } from '/js/Business/Tax/TaxValueModel.js';

export const inventoryTools = {
    // Transaction type IDs for order types
    TRANSACTION_TYPE_IDS: {
        SALES_ORDER: "01JRKA5XSRQHRCBY9A06M2J1RQ",
        PURCHASE_ORDER: "01K0SHCQVBWFD8NYJ5213AKVH3"
    },

    updateCostAmount(element) {
        this.updateAmount(element, `input[name$='.Quantity']`, `input[name$='.CostPrice']`, `input[name$='.CostAmount']`);
    },

    updateSalesAmount(element) {
        this.updateAmount(element, `input[name$='.Quantity']`, `input[name$='.SalesPrice']`, `input[name$='.SalesAmount']`);
    },

    updateAmount(element, quantitySelector, priceSelector, amountSelector) {
        const row = element.closest('tr');
        if (!row) return;

        const quantityInput = row.querySelector(quantitySelector);
        const priceInput = row.querySelector(priceSelector);
        const amountInput = row.querySelector(amountSelector);
        if (!quantityInput || !priceInput || !amountInput) return;

        const quantity = parseFloat(quantityInput.value) || 0;
        const price = parseFloat(priceInput.value) || 0;
        const amount = quantity * price;
        amountInput.value = isNaN(amount) ? "0.00" : amount.toFixed(2);

        this.updateTaxAmount(row, amountInput.value);
        this.updateNetAmount(row, amountSelector);
    },

    updateTaxAmount(row, amount) {
        const rowIndex = row.getAttribute('data-row-index');
        if (!rowIndex) return;

        const hiddenInput = row.querySelector(`input[name$='.InventoryTaxesJson']`);
        const taxAmountInput = row.querySelector(`input[name$='.TaxAmount']`);
        if (!hiddenInput || !taxAmountInput) return;

        let selectedTaxes = [];
        try {
            const rawTaxes = JSON.parse(hiddenInput.value);
            if (rawTaxes && Array.isArray(rawTaxes)) {
                selectedTaxes = rawTaxes.map(tax => new TaxValueModel(tax));
            }
        } catch (e) {
            selectedTaxes = [];
        }

        if (!selectedTaxes.length) {
            taxAmountInput.value = "0.00";
            return;
        }

        let totalTaxAmount = 0;
        selectedTaxes.forEach(tax => {
            const ratio = parseFloat(tax.InventoryTaxsRatio) || 0;
            const isAddition = tax.IsAddition ? 1 : -1;
            const taxAmount = (parseFloat(amount) || 0) * ratio * isAddition;
            tax.InventoryTaxsAmount = isNaN(taxAmount) ? 0 : parseFloat(taxAmount.toFixed(2));
            totalTaxAmount += tax.InventoryTaxsAmount;
        });

        // Convert TaxValueModel objects back to plain objects for JSON serialization
        const taxesForJson = selectedTaxes.map(tax => tax.toJson());
        hiddenInput.value = JSON.stringify(taxesForJson);
        taxAmountInput.value = isNaN(totalTaxAmount) ? "0.00" : totalTaxAmount.toFixed(2);
    },

    updateNetAmount(row, amountSelector) {
        const amountInput = row.querySelector(amountSelector);
        const taxAmountInput = row.querySelector(`input[name$='.TaxAmount']`);
        const netAmountInput = row.querySelector(`input[name$='.NetAmount']`);
        if (!amountInput || !taxAmountInput || !netAmountInput) return;

        const amount = parseFloat(amountInput.value) || 0;
        const tax = parseFloat(taxAmountInput.value) || 0;
        const net = amount + tax;

        netAmountInput.value = isNaN(net) ? "0.00" : net.toFixed(2);
    },

    updateProductUnitDropdown(index, productUnits) {
        if (!productUnits) return;

        const row = document.querySelector(`#InventoriesDataGridView tr[data-row-index="${index}"]`);
        const select = row?.querySelector(`select[name$='.ProductUnitId']`);
        if (!select) return;

        select.innerHTML = '<option value=""></option>';
        productUnits.$values.forEach(sub => {
            const opt = document.createElement("option");
            opt.value = sub.ProductUnit.Id;
            opt.text = sub.ProductUnit.UnitName;
            select.appendChild(opt);
        });
    },

    updateProductUnitRatio(index, productUnit) {
        if (!productUnit) return;

        const row = document.querySelector(`#InventoriesDataGridView tr[data-row-index="${index}"]`);
        if (!row) return;

        const salesPrice = row.querySelector(`input[name$='.SalesPrice']`);
        const unitQtyRatio = row.querySelector(`input[name$='.UnitQtyRatio']`);

        if (salesPrice) {
            salesPrice.value = productUnit.ProductUnitSalesPrice;
            salesPrice.dispatchEvent(new Event('change', { bubbles: true }));
        }

        if (unitQtyRatio) {
            unitQtyRatio.value = productUnit.ProductUnitRatio;
            unitQtyRatio.dispatchEvent(new Event('change', { bubbles: true }));
        }
    },

    // Check if a linked transaction line already exists in the inventory grid
    checkLinkedTransactionLineExists(linkedTransactionId, linkedTransactionLineId) {
        if (!linkedTransactionId || !linkedTransactionLineId) return false;

        const rows = document.querySelectorAll('#InventoriesDataGridView tbody tr');
        for (const row of rows) {
            const hiddenLinkedTransactionId = row.querySelector(`input[name$='.LinkedTransactionId']`)?.value;
            const hiddenLinkedTransactionLineId = row.querySelector(`input[name$='.LinkedTransactionLineId']`)?.value;
            
            if (hiddenLinkedTransactionId === linkedTransactionId && 
                hiddenLinkedTransactionLineId === linkedTransactionLineId) {
                return true;
            }
        }
        return false;
    },

    // Validate order line selection to prevent duplication
    validateOrderLineSelection(linkedTransactionId, linkedTransactionLineId, transactionTypeId) {
        // Only check for SalesOrder and PurchaseOrder transaction types
        if (transactionTypeId !== this.TRANSACTION_TYPE_IDS.SALES_ORDER && 
            transactionTypeId !== this.TRANSACTION_TYPE_IDS.PURCHASE_ORDER) {
            return true; // Allow for other transaction types
        }

        if (this.checkLinkedTransactionLineExists(linkedTransactionId, linkedTransactionLineId)) {
            alert('This order line has already been added to the inventory. Duplicate entries are not allowed.');
            return false;
        }
        return true;
    },

    buildInventoryColumns(config) {
        const {
            transactionType,
            products,
            stores,
            isSales = false,
            includeHidden = false,
            hiddenValue = null
        } = config;

        const columns = [];

        if (includeHidden && hiddenValue) {
            columns.push({ type: 'hidden', name: 'TransactionId', value: hiddenValue });
        }

        // Add hidden inputs for LinkedTransactionId and LinkedTransactionLineId
        columns.push(
            { type: 'hidden', name: 'LinkedTransactionId', value: '' },
            { type: 'hidden', name: 'LinkedTransactionLineId', value: '' }
        );

        columns.push(
            {
                type: 'select',
                generateSelect: (index, model) => `
                    <input type="hidden" name="${model}[${index}].TransactionTypeId" id="${model}_${index}__TransactionTypeId" value="${transactionType}" />
                    <input type="hidden" name="${model}[${index}].UnitQtyRatio" id="${model}_${index}__UnitQtyRatio" />
                    <select class="form-select" name="${model}[${index}].ProductId"
                            id="${model}_${index}__ProductId"
                            onchange="inventoryHandler.onProductChanged(this.value, ${index})"
                            data-val="true" data-val-required="Product Required.">
                        <option value=""></option>
                        ${products.$values.map(t => `<option value="${t.Id}">${t.ProductPurchasesDescription}</option>`).join("")}
                    </select>
                    <span class="text-danger" data-valmsg-for="${model}[${index}].ProductId" data-valmsg-replace="true"></span>`
            },
            {
                type: 'select',
                generateSelect: (index, model) => `
                    <select class="form-select" name="${model}[${index}].ProductUnitId"
                            id="${model}_${index}__ProductUnitId"
                            onchange="inventoryHandler.onUnitChanged(this.value, ${index})"
                            data-val="true" data-val-required="Product Unit Required.">
                        <option value=""></option>
                    </select>
                    <span class="text-danger" data-valmsg-for="${model}[${index}].ProductUnitId" data-valmsg-replace="true"></span>`
            },
            {
                type: 'input',
                name: 'Quantity',
                inputType: 'number',
                validationAttributes: 'data-val="true" data-val-required=" Quantity Required."',
                eventAttributes: 'onchange="inventoryHandler.onQuantityChanged(this)"'
            },
            {
                type: 'select',
                generateSelect: (index, model) => `
                    <select class="form-select" name="${model}[${index}].StoreId"
                            id="${model}_${index}__StoreId"
                            data-val="true" data-val-required="Store Required.">
                        <option value=""></option>
                        ${stores.$values.map(u => `<option value="${u.Value}">${u.Text}</option>`).join("")}
                    </select>
                    <span class="text-danger" data-valmsg-for="${model}[${index}].StoreId" data-valmsg-replace="true"></span>`
            },
            {
                type: 'input',
                name: isSales ? 'SalesPrice' : 'CostPrice',
                inputType: 'number',
                validationAttributes: `data-val="true" data-val-required="${isSales ? 'Sales' : 'Cost'} Price Required."`,
                eventAttributes: 'onchange="inventoryHandler.onQuantityChanged(this)"'
            },
            {
                type: 'input',
                name: isSales ? 'SalesAmount' : 'CostAmount',
                inputType: 'number',
                validationAttributes: `data-val="true" data-val-required="${isSales ? 'Sales' : 'Cost'} Amount Required."`,
                isReadOnly: true
            },
            {
                type: 'custom',
                render: (index, model) => `
                    <div class="input-group">
                        <input type="number" class="form-control"
                               name="${model}[${index}].TaxAmount" id="${model}_${index}__TaxAmount" readonly />
                        <input type="hidden" class="form-control"
                               name="${model}[${index}].InventoryTaxesJson" id="${model}_${index}__InventoryTaxesJson" />
                        <button type="button" class="btn btn-outline-secondary" 
                                name="${model}[${index}].InventoryTaxes" 
                                id="${model}_${index}__InventoryTaxes"
                                data-bs-toggle="modal" 
                                data-bs-target="#taxModal" 
                                onclick="inventoryHandler.onOpenSelectTaxType(${index})"
                                aria-label="Select taxes for this inventory row">
                            Select Taxes
                        </button>
                    </div>
                    <span class="text-danger" 
                          data-valmsg-for="${model}[${index}].TaxAmount" 
                          data-valmsg-replace="true"></span>`
            },
            {
                type: 'input',
                name: 'NetAmount',
                inputType: 'number',
                validationAttributes: 'data-val="true" data-val-required="Net Amount Required."',
                isReadOnly: true
            },
            {
                type: 'button',
                label: 'Remove'
            }
        );

        return columns;
    },

    addRow(config) {
        const {
            transactionType,
            products,
            stores,
            isSales = false,
            modelName = "Inventories",
            tableId = "InventoriesDataGridView",
            includeHidden = false,
            hiddenValues = null
        } = config;

        const columns = this.buildInventoryColumns({
            transactionType,
            products,
            stores,
            isSales,
            includeHidden,
            hiddenValues
        });

        dataGridViewTools.addRowToDataGridView(modelName, tableId, columns);
    }
};
